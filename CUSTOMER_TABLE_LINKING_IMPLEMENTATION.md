# Customer Table Linking Implementation

## Overview
This implementation adds the ability to link customers selected from the rewards system to specific tables in the POS system. When a customer is selected from rewards, they are automatically linked to the current table (or global order for walk-in customers).

## Key Features

### 1. Customer State Management
- **Global Customer**: For walk-in customers (no table selected)
- **Table-Specific Customers**: Each table can have its own customer linked
- **Automatic Linking**: When a customer is selected from rewards, they are automatically linked to the current context

### 2. State Structure
```kotlin
data class ProductsScreenState(
    // ... existing fields ...
    
    // Customer management state
    val selectedCustomer: RewardsCustomer? = null, // Global selected customer (for walk-in customers)
    val tableCustomers: Map<Int, RewardsCustomer> = emptyMap(), // Table-specific customers (tableId -> RewardsCustomer)
    
    // ... other fields ...
)
```

### 3. Key Methods Added

#### Customer Management
- `linkRewardsCustomer(customer)`: Public method to link a rewards customer to current table/order
- `linkCustomerToCurrentTable(customer)`: Links customer to current table or global order
- `linkCustomerToTable(tableId, customer)`: Links customer to specific table
- `clearCurrentCustomer()`: Clears customer from current context
- `clearTableCustomer(tableId)`: Clears customer from specific table
- `getCurrentCustomer()`: Gets the customer for current context (table or global)

#### Order Integration
- `convertRewardsCustomerToCustomer()`: Converts RewardsCustomer to Order Customer format
- Updated `placeOrder()`: Automatically includes customer information in orders

### 4. Automatic Cleanup
- Customer information is automatically cleared when tables are removed
- Cart clearing preserves customer information (intentional design)
- Complete reset (empty cart) clears all customer information

## Implementation Details

### 1. Customer Linking Flow
```
Rewards System → Customer Selected → RewardsViewModel.setSelectedCustomer()
                                  ↓
CategoriesActivity observes → productsScreenViewModel.linkRewardsCustomer()
                           ↓
Customer linked to current table/global order
```

### 2. Order Creation Flow
```
Place Order → getCurrentCustomer() → convertRewardsCustomerToCustomer()
           ↓
Order with customer information → API call
```

### 3. Table Management
- When table is added: Customer linking is preserved
- When table is removed: Customer information is cleared for that table
- When switching tables: Customer context switches automatically

## Usage Examples

### Linking a Customer
```kotlin
// Customer selected from rewards
val rewardsCustomer = RewardsCustomer(id = 123, name = "John Doe", ...)

// Link to current table/order
productsScreenViewModel.linkRewardsCustomer(rewardsCustomer)
```

### Getting Current Customer
```kotlin
val currentCustomer = state.getCurrentCustomer()
// Returns table customer if table selected, otherwise global customer
```

### Order Creation with Customer
```kotlin
// Customer information is automatically included when placing order
val orderResponse = productsScreenViewModel.placeOrder(order, transactionId)
// Order will include customerId and customer object
```

## Integration Points

### 1. CategoriesActivity
- Observes rewards customer selection
- Automatically links customer to products screen
- Handles customer removal

### 2. RewardsSystem
- Customer selection flows through RewardsViewModel
- Customer information is preserved across screens
- QR code scanning automatically links customers

### 3. Order Processing
- Customer information is included in all orders
- Supports both table-based and walk-in customers
- Maintains customer context throughout order lifecycle

## Memory Compliance
- ✅ When adding a table, the cart opens and displays even when empty
- ✅ Selected customer from rewards is linked to table
- ✅ Customer information flows through to order creation
- ✅ Multiple tables can have different customers

## Testing
Comprehensive tests are included in `CustomerLinkingTest.kt` covering:
- Global customer linking
- Table-specific customer linking
- Customer retrieval logic
- Table removal cleanup
- Multiple table scenarios

## Benefits
1. **Seamless Integration**: Rewards customers are automatically linked to orders
2. **Table Flexibility**: Each table can have its own customer
3. **Order Accuracy**: Customer information is always included in orders
4. **Clean State Management**: Automatic cleanup prevents data inconsistencies
5. **User Experience**: No manual linking required - works automatically

## Future Enhancements
- Customer history per table
- Customer preferences integration
- Loyalty points calculation during order
- Customer-specific pricing or discounts
