package com.thedasagroup.suminative.ui.products

import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.ui.products.cart.CourseStatus
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for course status functionality
 */
class CourseStatusTest {

    @Test
    fun `test CourseStatus enum values`() {
        assertEquals("Go", CourseStatus.GO.displayName)
        assertEquals("Preparing", CourseStatus.PREPARING.displayName)
        assertEquals("Complete", CourseStatus.COMPLETE.displayName)
    }

    @Test
    fun `test ProductsScreenState default course statuses`() {
        val state = ProductsScreenState()
        
        // Default state should have empty course statuses
        assertTrue(state.courseStatuses.isEmpty())
    }

    @Test
    fun `test ProductsScreenState with course statuses`() {
        val courseStatuses = mapOf(
            "course_starters" to CourseStatus.GO,
            "course_mains" to CourseStatus.PREPARING,
            "course_desserts" to CourseStatus.COMPLETE
        )
        
        val state = ProductsScreenState(courseStatuses = courseStatuses)
        
        assertEquals(CourseStatus.GO, state.courseStatuses["course_starters"])
        assertEquals(CourseStatus.PREPARING, state.courseStatuses["course_mains"])
        assertEquals(CourseStatus.COMPLETE, state.courseStatuses["course_desserts"])
    }

    @Test
    fun `test course status transitions`() {
        // Test the expected flow: GO -> PREPARING -> COMPLETE
        val initialStatus = CourseStatus.GO
        val preparingStatus = CourseStatus.PREPARING
        val completeStatus = CourseStatus.COMPLETE

        // Verify the flow makes sense
        assertNotEquals(initialStatus, preparingStatus)
        assertNotEquals(preparingStatus, completeStatus)
        assertNotEquals(initialStatus, completeStatus)

        // Verify display names are correct
        assertEquals("Go", initialStatus.displayName)
        assertEquals("Preparing", preparingStatus.displayName)
        assertEquals("Complete", completeStatus.displayName)
    }

    @Test
    fun `test active course tracking`() {
        val state = ProductsScreenState(
            currentActiveCourse = "course_starters",
            availableCourses = listOf(
                MealCourse("course_starters", "Starters", "Starters"),
                MealCourse("course_mains", "Mains", "Mains"),
                MealCourse("course_desserts", "Desserts", "Desserts")
            )
        )

        assertEquals("course_starters", state.currentActiveCourse)
        assertEquals(3, state.availableCourses.size)
    }

    @Test
    fun `test course sequence`() {
        val courses = listOf(
            MealCourse("course_starters", "Starters", "Starters"),
            MealCourse("course_mains", "Mains", "Mains"),
            MealCourse("course_desserts", "Desserts", "Desserts")
        )

        // Test course order
        assertEquals("course_starters", courses[0].id)
        assertEquals("course_mains", courses[1].id)
        assertEquals("course_desserts", courses[2].id)

        // Test finding next course
        val currentIndex = courses.indexOfFirst { it.id == "course_starters" }
        val nextCourse = if (currentIndex < courses.size - 1) courses[currentIndex + 1] else courses[0]
        assertEquals("course_mains", nextCourse.id)
    }

    @Test
    fun `test cart items selection logic`() {
        // Test table selection logic
        val tableId = 101
        val globalCartItems = listOf(
            CartItemWithCourse(
                cart = Cart(quantity = 1, price = 10.0),
                courseId = "course_starters"
            )
        )
        val tableCartItems = mapOf(
            tableId to listOf(
                CartItemWithCourse(
                    cart = Cart(quantity = 2, price = 15.0),
                    courseId = "course_mains"
                )
            )
        )

        // When table is selected, should use table-specific items
        val tableSelectedItems = tableCartItems[tableId]?.filter { it.courseId == "course_mains" } ?: emptyList()
        assertEquals(1, tableSelectedItems.size)
        assertEquals("course_mains", tableSelectedItems[0].courseId)

        // When no table selected, should use global items
        val globalSelectedItems = globalCartItems.filter { it.courseId == "course_starters" }
        assertEquals(1, globalSelectedItems.size)
        assertEquals("course_starters", globalSelectedItems[0].courseId)
    }

    @Test
    fun `test table-based course statuses`() {
        val table1Id = 101
        val table2Id = 102

        val tableCourseStatuses = mapOf(
            table1Id to mapOf(
                "course_starters" to CourseStatus.PREPARING,
                "course_mains" to CourseStatus.GO
            ),
            table2Id to mapOf(
                "course_starters" to CourseStatus.COMPLETE,
                "course_desserts" to CourseStatus.PREPARING
            )
        )

        val state = ProductsScreenState(tableCourseStatuses = tableCourseStatuses)

        // Test table 1 statuses
        assertEquals(CourseStatus.PREPARING, state.tableCourseStatuses[table1Id]?.get("course_starters"))
        assertEquals(CourseStatus.GO, state.tableCourseStatuses[table1Id]?.get("course_mains"))

        // Test table 2 statuses
        assertEquals(CourseStatus.COMPLETE, state.tableCourseStatuses[table2Id]?.get("course_starters"))
        assertEquals(CourseStatus.PREPARING, state.tableCourseStatuses[table2Id]?.get("course_desserts"))

        // Test non-existent course defaults to null (will be handled as GO in getCourseStatus)
        assertNull(state.tableCourseStatuses[table1Id]?.get("course_desserts"))
    }

    @Test
    fun `test table active courses`() {
        val table1Id = 101
        val table2Id = 102

        val tableActiveCourses = mapOf(
            table1Id to "course_mains",
            table2Id to "course_desserts"
        )

        val state = ProductsScreenState(tableActiveCourses = tableActiveCourses)

        assertEquals("course_mains", state.tableActiveCourses[table1Id])
        assertEquals("course_desserts", state.tableActiveCourses[table2Id])
        assertNull(state.tableActiveCourses[999]) // Non-existent table
    }

    @Test
    fun `test table removal logic`() {
        val table1Id = 101
        val table2Id = 102

        val initialTableStatuses = mapOf(
            table1Id to mapOf("course_starters" to CourseStatus.PREPARING),
            table2Id to mapOf("course_mains" to CourseStatus.GO)
        )

        val initialActiveStatuses = mapOf(
            table1Id to "course_starters",
            table2Id to "course_mains"
        )

        // Simulate table removal
        val updatedTableStatuses = initialTableStatuses.toMutableMap()
        updatedTableStatuses.remove(table1Id)

        val updatedActiveStatuses = initialActiveStatuses.toMutableMap()
        updatedActiveStatuses.remove(table1Id)

        // Verify table 1 is removed
        assertFalse(updatedTableStatuses.containsKey(table1Id))
        assertFalse(updatedActiveStatuses.containsKey(table1Id))

        // Verify table 2 remains
        assertTrue(updatedTableStatuses.containsKey(table2Id))
        assertTrue(updatedActiveStatuses.containsKey(table2Id))
        assertEquals(CourseStatus.GO, updatedTableStatuses[table2Id]?.get("course_mains"))
        assertEquals("course_mains", updatedActiveStatuses[table2Id])
    }

    @Test
    fun `test hasGlobalCartItems returns true when global cart has items`() {
        val globalCartItems = listOf(
            Cart(quantity = 1, price = 10.0)
        )
        val globalCartItemsWithCourses = listOf(
            CartItemWithCourse(
                cart = Cart(quantity = 2, price = 15.0),
                courseId = "course_starters"
            )
        )

        val order = Order(carts = globalCartItems)
        val state = ProductsScreenState(
            order = order,
            globalCartItemsWithCourses = globalCartItemsWithCourses,
            selectedTables = emptyList() // No tables selected
        )

        // Should return true when global cart has items and no table selected
        assertTrue(globalCartItems.isNotEmpty() || globalCartItemsWithCourses.isNotEmpty())
    }

    @Test
    fun `test hasGlobalCartItems returns false when table is selected`() {
        val globalCartItems = listOf(
            Cart(quantity = 1, price = 10.0)
        )
        val selectedTables = listOf(
            AreaTableSelectionHelper.AreaTableSelection(
                areaId = 1,
                areaName = "Main Area",
                tableId = 101,
                tableName = "Table 1",
                tableCapacity = 4
            )
        )

        val order = Order(carts = globalCartItems)
        val state = ProductsScreenState(
            order = order,
            selectedTables = selectedTables,
            selectedTableIndex = 0 // Table is selected
        )

        // Should return false when table is selected, regardless of global cart items
        assertTrue(selectedTables.isNotEmpty())
    }

    @Test
    fun `test clearGlobalCart clears all global data`() {
        val initialOrder = Order(
            carts = listOf(Cart(quantity = 1, price = 10.0)),
            netPayable = 10.0,
            tax = 1.0,
            totalPrice = 11.0
        )
        val initialGlobalCartItems = listOf(
            CartItemWithCourse(
                cart = Cart(quantity = 2, price = 15.0),
                courseId = "course_starters"
            )
        )
        val initialCourseStatuses = mapOf(
            "course_starters" to CourseStatus.PREPARING
        )

        val state = ProductsScreenState(
            order = initialOrder,
            globalCartItemsWithCourses = initialGlobalCartItems,
            courseStatuses = initialCourseStatuses,
            currentActiveCourse = "course_mains"
        )

        // Simulate clearing global cart
        val clearedOrder = Order(
            carts = emptyList(),
            netPayable = 0.0,
            tax = 0.0,
            totalPrice = 0.0
        )
        val clearedState = state.copy(
            order = clearedOrder,
            globalCartItemsWithCourses = emptyList(),
            courseStatuses = emptyMap(),
            currentActiveCourse = state.availableCourses.firstOrNull()?.id
        )

        // Verify all global data is cleared
        assertTrue(clearedState.order.carts?.isEmpty() == true)
        assertTrue(clearedState.globalCartItemsWithCourses.isEmpty())
        assertTrue(clearedState.courseStatuses.isEmpty())
        assertEquals("course_starters", clearedState.currentActiveCourse) // Reset to first course
    }

    @Test
    fun `test removeCourse removes course successfully`() {
        val initialCourses = listOf(
            MealCourse("course_starters", "Starters", "Starters"),
            MealCourse("course_mains", "Mains", "Mains"),
            MealCourse("course_desserts", "Desserts", "Desserts")
        )

        val state = ProductsScreenState(availableCourses = initialCourses)

        // Simulate removing a course
        val updatedCourses = initialCourses.toMutableList()
        updatedCourses.removeAll { it.id == "course_mains" }

        val updatedState = state.copy(availableCourses = updatedCourses)

        // Verify course was removed
        assertEquals(2, updatedState.availableCourses.size)
        assertFalse(updatedState.availableCourses.any { it.id == "course_mains" })
        assertTrue(updatedState.availableCourses.any { it.id == "course_starters" })
        assertTrue(updatedState.availableCourses.any { it.id == "course_desserts" })
    }

    @Test
    fun `test removeCourse prevents removing last course`() {
        val singleCourse = listOf(
            MealCourse("course_starters", "Starters", "Starters")
        )

        val state = ProductsScreenState(availableCourses = singleCourse)

        // Should not be able to remove the last course
        assertEquals(1, state.availableCourses.size)

        // Simulate the check that prevents removal
        val canRemove = state.availableCourses.size > 1
        assertFalse(canRemove)
    }

    @Test
    fun `test editCourse updates course name`() {
        val initialCourses = listOf(
            MealCourse("course_starters", "Starters", "Starters"),
            MealCourse("course_mains", "Mains", "Mains"),
            MealCourse("course_desserts", "Desserts", "Desserts")
        )

        val state = ProductsScreenState(availableCourses = initialCourses)

        // Simulate editing a course
        val updatedCourses = initialCourses.map { course ->
            if (course.id == "course_mains") {
                course.copy(name = "Main Courses", displayName = "Main Courses")
            } else {
                course
            }
        }

        val updatedState = state.copy(availableCourses = updatedCourses)

        // Verify course was updated
        val updatedCourse = updatedState.availableCourses.find { it.id == "course_mains" }
        assertNotNull(updatedCourse)
        assertEquals("Main Courses", updatedCourse?.name)
        assertEquals("Main Courses", updatedCourse?.displayName)
    }

    @Test
    fun `test course removal cleans up related data`() {
        val initialCourses = listOf(
            MealCourse("course_starters", "Starters", "Starters"),
            MealCourse("course_mains", "Mains", "Mains"),
            MealCourse("course_desserts", "Desserts", "Desserts")
        )

        val initialCourseStatuses = mapOf(
            "course_starters" to CourseStatus.GO,
            "course_mains" to CourseStatus.PREPARING,
            "course_desserts" to CourseStatus.COMPLETE
        )

        val initialTableCourseStatuses = mapOf(
            101 to mapOf(
                "course_starters" to CourseStatus.GO,
                "course_mains" to CourseStatus.PREPARING
            )
        )

        val initialTableActiveCourses = mapOf(
            101 to "course_mains"
        )

        val state = ProductsScreenState(
            availableCourses = initialCourses,
            courseStatuses = initialCourseStatuses,
            tableCourseStatuses = initialTableCourseStatuses,
            tableActiveCourses = initialTableActiveCourses,
            selectedCourseForNewItems = "course_mains",
            currentActiveCourse = "course_mains"
        )

        // Simulate removing course_mains
        val updatedCourses = initialCourses.toMutableList()
        updatedCourses.removeAll { it.id == "course_mains" }

        val updatedCourseStatuses = initialCourseStatuses.toMutableMap()
        updatedCourseStatuses.remove("course_mains")

        val updatedTableCourseStatuses = initialTableCourseStatuses.mapValues { (_, courseStatuses) ->
            courseStatuses.toMutableMap().apply { remove("course_mains") }
        }

        val updatedTableActiveCourses = initialTableActiveCourses.mapValues { (_, activeCourse) ->
            if (activeCourse == "course_mains") {
                updatedCourses.firstOrNull()?.id ?: "course_starters"
            } else {
                activeCourse
            }
        }

        val updatedState = state.copy(
            availableCourses = updatedCourses,
            courseStatuses = updatedCourseStatuses,
            tableCourseStatuses = updatedTableCourseStatuses,
            tableActiveCourses = updatedTableActiveCourses,
            selectedCourseForNewItems = updatedCourses.firstOrNull()?.id ?: "course_starters",
            currentActiveCourse = updatedCourses.firstOrNull()?.id
        )

        // Verify cleanup
        assertEquals(2, updatedState.availableCourses.size)
        assertFalse(updatedState.courseStatuses.containsKey("course_mains"))
        assertFalse(updatedState.tableCourseStatuses[101]?.containsKey("course_mains") == true)
        assertEquals("course_starters", updatedState.tableActiveCourses[101])
        assertEquals("course_starters", updatedState.selectedCourseForNewItems)
        assertEquals("course_starters", updatedState.currentActiveCourse)
    }

    @Test
    fun `test same item can be added to different courses`() {
        // Create a store item
        val storeItem = StoreItem(
            id = 1,
            name = "Pizza",
            price = 15.99,
            courseId = "course_starters"
        )

        // Create another instance of the same item for a different course
        val storeItemForMains = storeItem.copy(courseId = "course_mains")

        // Create cart items
        val cartItemStarters = Cart(
            storeItem = storeItem,
            quantity = 1,
            price = 15.99,
            netPayable = 15.99
        )

        val cartItemMains = Cart(
            storeItem = storeItemForMains,
            quantity = 2,
            price = 15.99,
            netPayable = 31.98
        )

        // Verify that the items have different course IDs
        assertEquals("course_starters", cartItemStarters.storeItem?.courseId)
        assertEquals("course_mains", cartItemMains.storeItem?.courseId)

        // Verify that they are treated as different items even though they have the same base ID
        assertEquals(1, cartItemStarters.storeItem?.id)
        assertEquals(1, cartItemMains.storeItem?.id)
        assertNotEquals(cartItemStarters.storeItem?.courseId, cartItemMains.storeItem?.courseId)

        // Verify quantities are independent
        assertEquals(1, cartItemStarters.quantity)
        assertEquals(2, cartItemMains.quantity)
    }

    @Test
    fun `test cart item uniqueness with course ID and UUID`() {
        val baseStoreItem = StoreItem(
            id = 1,
            name = "Burger",
            price = 12.50
        )

        // Create cart items for different courses
        val startersItem = Cart(
            storeItem = baseStoreItem.copy(courseId = "course_starters"),
            quantity = 1,
            price = 12.50
        )

        val mainsItem = Cart(
            storeItem = baseStoreItem.copy(courseId = "course_mains"),
            quantity = 3,
            price = 12.50
        )

        val dessertsItem = Cart(
            storeItem = baseStoreItem.copy(courseId = "course_desserts"),
            quantity = 2,
            price = 12.50
        )

        // Verify each cart item has a unique UUID
        assertNotEquals(startersItem.uuid, mainsItem.uuid)
        assertNotEquals(mainsItem.uuid, dessertsItem.uuid)
        assertNotEquals(startersItem.uuid, dessertsItem.uuid)

        // Verify course IDs are different
        assertEquals("course_starters", startersItem.storeItem?.courseId)
        assertEquals("course_mains", mainsItem.storeItem?.courseId)
        assertEquals("course_desserts", dessertsItem.storeItem?.courseId)

        // Verify they all have the same base item ID but different course assignments
        assertEquals(1, startersItem.storeItem?.id)
        assertEquals(1, mainsItem.storeItem?.id)
        assertEquals(1, dessertsItem.storeItem?.id)
    }

    @Test
    fun `test course assignment uses cart UUID for uniqueness`() {
        val storeItem = StoreItem(
            id = 1,
            name = "Pasta",
            price = 18.50
        )

        // Create two cart items with the same store item but different UUIDs
        val cartItem1 = Cart(
            storeItem = storeItem.copy(courseId = "course_starters"),
            quantity = 1,
            price = 18.50
        )

        val cartItem2 = Cart(
            storeItem = storeItem.copy(courseId = "course_mains"),
            quantity = 2,
            price = 18.50
        )

        // Verify they have different UUIDs even though they have the same store item ID
        assertNotEquals(cartItem1.uuid, cartItem2.uuid)
        assertEquals(cartItem1.storeItem?.id, cartItem2.storeItem?.id)
        assertNotEquals(cartItem1.storeItem?.courseId, cartItem2.storeItem?.courseId)

        // This demonstrates that course assignment should use cart UUID, not store item ID
        // to properly handle the same item in different courses
    }

    @Test
    fun `test cart item removal by UUID`() {
        val storeItem1 = StoreItem(
            id = 1,
            name = "Pizza",
            price = 15.99,
            courseId = "course_starters"
        )

        val storeItem2 = StoreItem(
            id = 1,
            name = "Pizza",
            price = 15.99,
            courseId = "course_mains"
        )

        // Create cart items with same store item but different courses
        val cartItem1 = Cart(
            storeItem = storeItem1,
            quantity = 1,
            price = 15.99,
            netPayable = 15.99
        )

        val cartItem2 = Cart(
            storeItem = storeItem2,
            quantity = 2,
            price = 15.99,
            netPayable = 31.98
        )

        // Create a list with both items
        val cartList = mutableListOf(cartItem1, cartItem2)

        // Remove the first item by UUID
        cartList.removeAll { it.uuid == cartItem1.uuid }

        // Verify only the second item remains
        assertEquals(1, cartList.size)
        assertEquals(cartItem2.uuid, cartList[0].uuid)
        assertEquals("course_mains", cartList[0].storeItem?.courseId)
        assertEquals(2, cartList[0].quantity)

        // Verify the removed item is not in the list
        assertFalse(cartList.any { it.uuid == cartItem1.uuid })
    }

    @Test
    fun `test item removal works correctly with multiple same items in different courses`() {
        val storeItem = StoreItem(
            id = 1,
            name = "Burger",
            price = 12.50
        )

        // Create multiple cart items with the same store item but different courses
        val cartItem1 = Cart(
            storeItem = storeItem.copy(courseId = "course_starters"),
            quantity = 1,
            price = 12.50,
            netPayable = 12.50
        )

        val cartItem2 = Cart(
            storeItem = storeItem.copy(courseId = "course_mains"),
            quantity = 2,
            price = 12.50,
            netPayable = 25.00
        )

        val cartItem3 = Cart(
            storeItem = storeItem.copy(courseId = "course_desserts"),
            quantity = 1,
            price = 12.50,
            netPayable = 12.50
        )

        // Create initial cart list
        val initialCartList = mutableListOf(cartItem1, cartItem2, cartItem3)
        assertEquals(3, initialCartList.size)

        // Remove the middle item (cartItem2) using UUID
        initialCartList.removeAll { it.uuid == cartItem2.uuid }

        // Verify only the correct item was removed
        assertEquals(2, initialCartList.size)
        assertTrue(initialCartList.any { it.uuid == cartItem1.uuid })
        assertFalse(initialCartList.any { it.uuid == cartItem2.uuid })
        assertTrue(initialCartList.any { it.uuid == cartItem3.uuid })

        // Verify the remaining items have the correct course assignments
        val remainingItem1 = initialCartList.find { it.uuid == cartItem1.uuid }
        val remainingItem3 = initialCartList.find { it.uuid == cartItem3.uuid }

        assertEquals("course_starters", remainingItem1?.storeItem?.courseId)
        assertEquals("course_desserts", remainingItem3?.storeItem?.courseId)
    }

    @Test
    fun `test Send to Kitchen course logic - Course 1 Go to Preparing and Course 2 Go`() {
        // Setup courses
        val course1 = MealCourse("Course 1")
        val course2 = MealCourse("Course 2")
        val availableCourses = listOf(course1, course2)

        // Initial state: Course 1 is GO, Course 2 is not set (defaults to GO)
        val initialCourseStatuses = mapOf(
            "Course 1" to CourseStatus.GO
        )

        val state = ProductsScreenState(
            availableCourses = availableCourses,
            courseStatuses = initialCourseStatuses
        )

        // Simulate Send to Kitchen logic
        // When Course 1 is GO or PREPARING, it should become PREPARING and Course 2 should become GO
        val updatedCourseStatuses = initialCourseStatuses.toMutableMap()
        updatedCourseStatuses["Course 1"] = CourseStatus.PREPARING
        updatedCourseStatuses["Course 2"] = CourseStatus.GO

        val updatedState = state.copy(
            courseStatuses = updatedCourseStatuses,
            currentActiveCourse = "Course 2" // Active course moves to Course 2
        )

        // Verify the logic
        assertEquals(CourseStatus.PREPARING, updatedState.courseStatuses["Course 1"])
        assertEquals(CourseStatus.GO, updatedState.courseStatuses["Course 2"])
        assertEquals("Course 2", updatedState.currentActiveCourse)
    }

    @Test
    fun `test Send to Kitchen course logic - Course 1 already Preparing`() {
        // Setup courses
        val course1 = MealCourse("Course 1")
        val course2 = MealCourse("Course 2")
        val availableCourses = listOf(course1, course2)

        // Initial state: Course 1 is already PREPARING
        val initialCourseStatuses = mapOf(
            "Course 1" to CourseStatus.PREPARING
        )

        val state = ProductsScreenState(
            availableCourses = availableCourses,
            courseStatuses = initialCourseStatuses
        )

        // Simulate Send to Kitchen logic
        // When Course 1 is already PREPARING, it should remain PREPARING and Course 2 should become GO
        val updatedCourseStatuses = initialCourseStatuses.toMutableMap()
        updatedCourseStatuses["Course 1"] = CourseStatus.PREPARING // Remains the same
        updatedCourseStatuses["Course 2"] = CourseStatus.GO

        val updatedState = state.copy(
            courseStatuses = updatedCourseStatuses,
            currentActiveCourse = "Course 2"
        )

        // Verify the logic
        assertEquals(CourseStatus.PREPARING, updatedState.courseStatuses["Course 1"])
        assertEquals(CourseStatus.GO, updatedState.courseStatuses["Course 2"])
        assertEquals("Course 2", updatedState.currentActiveCourse)
    }

    @Test
    fun `test Course 1 Preparing triggers Course 2 Go`() {
        // Setup courses
        val course1 = MealCourse("Course 1")
        val course2 = MealCourse("Course 2")
        val availableCourses = listOf(course1, course2)

        // Initial state: Both courses are GO
        val initialCourseStatuses = mapOf(
            "Course 1" to CourseStatus.GO,
            "Course 2" to CourseStatus.GO
        )

        val state = ProductsScreenState(
            availableCourses = availableCourses,
            courseStatuses = initialCourseStatuses,
            currentActiveCourse = "Course 1"
        )

        // Simulate updating Course 1 to PREPARING
        val updatedCourseStatuses = initialCourseStatuses.toMutableMap()
        updatedCourseStatuses["Course 1"] = CourseStatus.PREPARING
        // The logic should automatically set Course 2 to GO and make it active
        updatedCourseStatuses["Course 2"] = CourseStatus.GO

        val updatedState = state.copy(
            courseStatuses = updatedCourseStatuses,
            currentActiveCourse = "Course 2" // Active course moves to Course 2
        )

        // Verify the logic
        assertEquals(CourseStatus.PREPARING, updatedState.courseStatuses["Course 1"])
        assertEquals(CourseStatus.GO, updatedState.courseStatuses["Course 2"])
        assertEquals("Course 2", updatedState.currentActiveCourse)
    }

    @Test
    fun `test Course 2 Go click marks Course 1 Complete`() {
        // Setup courses
        val course1 = MealCourse("Course 1")
        val course2 = MealCourse("Course 2")
        val availableCourses = listOf(course1, course2)

        // Initial state: Course 1 is PREPARING, Course 2 is GO
        val initialCourseStatuses = mapOf(
            "Course 1" to CourseStatus.PREPARING,
            "Course 2" to CourseStatus.GO
        )

        val state = ProductsScreenState(
            availableCourses = availableCourses,
            courseStatuses = initialCourseStatuses,
            currentActiveCourse = "Course 2"
        )

        // Simulate clicking Go on Course 2
        // This should mark Course 1 as COMPLETE and Course 2 as PREPARING
        val updatedCourseStatuses = initialCourseStatuses.toMutableMap()
        updatedCourseStatuses["Course 1"] = CourseStatus.COMPLETE // Course 1 becomes complete
        updatedCourseStatuses["Course 2"] = CourseStatus.PREPARING // Course 2 becomes preparing

        val updatedState = state.copy(
            courseStatuses = updatedCourseStatuses
        )

        // Verify the logic
        assertEquals(CourseStatus.COMPLETE, updatedState.courseStatuses["Course 1"])
        assertEquals(CourseStatus.PREPARING, updatedState.courseStatuses["Course 2"])
    }

    @Test
    fun `test table-based Send to Kitchen course logic`() {
        // Setup courses
        val course1 = MealCourse("Course 1")
        val course2 = MealCourse("Course 2")
        val availableCourses = listOf(course1, course2)
        val tableId = 101

        // Initial state: Table-specific Course 1 is GO
        val initialTableCourseStatuses = mapOf(
            tableId to mapOf(
                "Course 1" to CourseStatus.GO
            )
        )

        val state = ProductsScreenState(
            tableAvailableCourses = mapOf(tableId to availableCourses),
            tableCourseStatuses = initialTableCourseStatuses,
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = tableId,
                    tableName = "Table 1",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0
        )

        // Simulate Send to Kitchen logic for table
        val updatedTableCourseStatuses = initialTableCourseStatuses.toMutableMap()
        val tableStatuses = updatedTableCourseStatuses[tableId]?.toMutableMap() ?: mutableMapOf()
        tableStatuses["Course 1"] = CourseStatus.PREPARING
        tableStatuses["Course 2"] = CourseStatus.GO
        updatedTableCourseStatuses[tableId] = tableStatuses

        val updatedTableActiveCourses = mapOf(tableId to "Course 2")

        val updatedState = state.copy(
            tableCourseStatuses = updatedTableCourseStatuses,
            tableActiveCourses = updatedTableActiveCourses
        )

        // Verify the table-specific logic
        assertEquals(CourseStatus.PREPARING, updatedState.tableCourseStatuses[tableId]?.get("Course 1"))
        assertEquals(CourseStatus.GO, updatedState.tableCourseStatuses[tableId]?.get("Course 2"))
        assertEquals("Course 2", updatedState.tableActiveCourses[tableId])
    }

    @Test
    fun `test course status flow integration`() {
        // Setup courses
        val course1 = MealCourse("Course 1")
        val course2 = MealCourse("Course 2")
        val course3 = MealCourse("Course 3")
        val availableCourses = listOf(course1, course2, course3)

        // Initial state: All courses are GO
        var courseStatuses = mapOf(
            "Course 1" to CourseStatus.GO,
            "Course 2" to CourseStatus.GO,
            "Course 3" to CourseStatus.GO
        )

        var state = ProductsScreenState(
            availableCourses = availableCourses,
            courseStatuses = courseStatuses,
            currentActiveCourse = "Course 1"
        )

        // Step 1: Send to Kitchen - Course 1 becomes PREPARING, Course 2 becomes GO and active
        courseStatuses = courseStatuses.toMutableMap().apply {
            this["Course 1"] = CourseStatus.PREPARING
            this["Course 2"] = CourseStatus.GO
        }
        state = state.copy(
            courseStatuses = courseStatuses,
            currentActiveCourse = "Course 2"
        )

        assertEquals(CourseStatus.PREPARING, state.courseStatuses["Course 1"])
        assertEquals(CourseStatus.GO, state.courseStatuses["Course 2"])
        assertEquals("Course 2", state.currentActiveCourse)

        // Step 2: Click Go on Course 2 - Course 1 becomes COMPLETE, Course 2 becomes PREPARING
        courseStatuses = courseStatuses.toMutableMap().apply {
            this["Course 1"] = CourseStatus.COMPLETE
            this["Course 2"] = CourseStatus.PREPARING
        }
        state = state.copy(courseStatuses = courseStatuses)

        assertEquals(CourseStatus.COMPLETE, state.courseStatuses["Course 1"])
        assertEquals(CourseStatus.PREPARING, state.courseStatuses["Course 2"])

        // Step 3: Course 2 is PREPARING, so Course 3 should become GO and active
        courseStatuses = courseStatuses.toMutableMap().apply {
            this["Course 3"] = CourseStatus.GO
        }
        state = state.copy(
            courseStatuses = courseStatuses,
            currentActiveCourse = "Course 3"
        )

        assertEquals(CourseStatus.COMPLETE, state.courseStatuses["Course 1"])
        assertEquals(CourseStatus.PREPARING, state.courseStatuses["Course 2"])
        assertEquals(CourseStatus.GO, state.courseStatuses["Course 3"])
        assertEquals("Course 3", state.currentActiveCourse)
    }

    @Test
    fun `test Course 1 Go click sets next course to Go`() {
        // Setup courses
        val course1 = MealCourse("Course 1")
        val course2 = MealCourse("Course 2")
        val availableCourses = listOf(course1, course2)

        // Initial state: Course 1 is GO, Course 2 is not set (defaults to GO)
        val initialCourseStatuses = mapOf(
            "Course 1" to CourseStatus.GO
        )

        val state = ProductsScreenState(
            availableCourses = availableCourses,
            courseStatuses = initialCourseStatuses,
            currentActiveCourse = "Course 1"
        )

        // Simulate clicking Go on Course 1
        // This should set Course 2 to Go status
        val updatedCourseStatuses = initialCourseStatuses.toMutableMap()
        updatedCourseStatuses["Course 2"] = CourseStatus.GO

        val updatedState = state.copy(
            courseStatuses = updatedCourseStatuses
        )

        // Verify the logic
        assertEquals(CourseStatus.GO, updatedState.courseStatuses["Course 1"])
        assertEquals(CourseStatus.GO, updatedState.courseStatuses["Course 2"])
    }

    @Test
    fun `test Course 1 Go click with no Course 2 does nothing`() {
        // Setup courses - only Course 1
        val course1 = MealCourse("Course 1")
        val availableCourses = listOf(course1)

        // Initial state: Only Course 1 exists
        val initialCourseStatuses = mapOf(
            "Course 1" to CourseStatus.GO
        )

        val state = ProductsScreenState(
            availableCourses = availableCourses,
            courseStatuses = initialCourseStatuses,
            currentActiveCourse = "Course 1"
        )

        // Simulate clicking Go on Course 1 when no Course 2 exists
        // Should not cause any errors or changes to non-existent courses
        val updatedState = state.copy()

        // Verify no changes to Course 1 and no Course 2 created
        assertEquals(CourseStatus.GO, updatedState.courseStatuses["Course 1"])
        assertNull(updatedState.courseStatuses["Course 2"])
        assertEquals(1, updatedState.availableCourses.size)
    }

    @Test
    fun `test new course creation when previous course is Preparing`() {
        // Setup initial courses
        val course1 = MealCourse("Course 1")
        val availableCourses = listOf(course1)

        // Initial state: Course 1 is PREPARING
        val initialCourseStatuses = mapOf(
            "Course 1" to CourseStatus.PREPARING
        )

        val state = ProductsScreenState(
            availableCourses = availableCourses,
            courseStatuses = initialCourseStatuses,
            currentActiveCourse = "Course 1"
        )

        // Simulate adding a new course when Course 1 is Preparing
        val course2 = MealCourse("Course 2")
        val updatedCourses = availableCourses.toMutableList()
        updatedCourses.add(course2)

        val updatedCourseStatuses = initialCourseStatuses.toMutableMap()
        // New course should be set to Go because previous course is Preparing
        updatedCourseStatuses["Course 2"] = CourseStatus.GO

        val updatedState = state.copy(
            availableCourses = updatedCourses,
            courseStatuses = updatedCourseStatuses,
            currentActiveCourse = "Course 2" // New course becomes active
        )

        // Verify the logic
        assertEquals(CourseStatus.PREPARING, updatedState.courseStatuses["Course 1"])
        assertEquals(CourseStatus.GO, updatedState.courseStatuses["Course 2"])
        assertEquals("Course 2", updatedState.currentActiveCourse)
        assertEquals(2, updatedState.availableCourses.size)
    }

    @Test
    fun `test new course creation when previous course is not Preparing`() {
        // Setup initial courses
        val course1 = MealCourse("Course 1")
        val availableCourses = listOf(course1)

        // Initial state: Course 1 is GO (not Preparing)
        val initialCourseStatuses = mapOf(
            "Course 1" to CourseStatus.GO
        )

        val state = ProductsScreenState(
            availableCourses = availableCourses,
            courseStatuses = initialCourseStatuses,
            currentActiveCourse = "Course 1"
        )

        // Simulate adding a new course when Course 1 is not Preparing
        val course2 = MealCourse("Course 2")
        val updatedCourses = availableCourses.toMutableList()
        updatedCourses.add(course2)

        val updatedCourseStatuses = initialCourseStatuses.toMutableMap()
        // New course should not be automatically set to Go
        // (it will default to Go when getCourseStatus is called, but not explicitly set)

        val updatedState = state.copy(
            availableCourses = updatedCourses,
            courseStatuses = updatedCourseStatuses,
            currentActiveCourse = "Course 1" // Active course remains the same
        )

        // Verify the logic
        assertEquals(CourseStatus.GO, updatedState.courseStatuses["Course 1"])
        assertNull(updatedState.courseStatuses["Course 2"]) // Not explicitly set
        assertEquals("Course 1", updatedState.currentActiveCourse)
        assertEquals(2, updatedState.availableCourses.size)
    }

    @Test
    fun `test table-based new course creation when previous course is Preparing`() {
        // Setup initial courses
        val course1 = MealCourse("Course 1")
        val availableCourses = listOf(course1)
        val tableId = 101

        // Initial state: Table-specific Course 1 is PREPARING
        val initialTableCourseStatuses = mapOf(
            tableId to mapOf(
                "Course 1" to CourseStatus.PREPARING
            )
        )

        val state = ProductsScreenState(
            tableAvailableCourses = mapOf(tableId to availableCourses),
            tableCourseStatuses = initialTableCourseStatuses,
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = tableId,
                    tableName = "Table 1",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0
        )

        // Simulate adding a new course for the table
        val course2 = MealCourse("Course 2")
        val updatedCourses = availableCourses.toMutableList()
        updatedCourses.add(course2)

        val updatedTableCourseStatuses = initialTableCourseStatuses.toMutableMap()
        val tableStatuses = updatedTableCourseStatuses[tableId]?.toMutableMap() ?: mutableMapOf()
        // New course should be set to Go because previous course is Preparing
        tableStatuses["Course 2"] = CourseStatus.GO
        updatedTableCourseStatuses[tableId] = tableStatuses

        val updatedTableActiveCourses = mapOf(tableId to "Course 2")

        val updatedState = state.copy(
            tableAvailableCourses = mapOf(tableId to updatedCourses),
            tableCourseStatuses = updatedTableCourseStatuses,
            tableActiveCourses = updatedTableActiveCourses
        )

        // Verify the table-specific logic
        assertEquals(CourseStatus.PREPARING, updatedState.tableCourseStatuses[tableId]?.get("Course 1"))
        assertEquals(CourseStatus.GO, updatedState.tableCourseStatuses[tableId]?.get("Course 2"))
        assertEquals("Course 2", updatedState.tableActiveCourses[tableId])
        assertEquals(2, updatedState.tableAvailableCourses[tableId]?.size)
    }

    @Test
    fun `test complete course flow with new logic`() {
        // Setup courses
        val course1 = MealCourse("Course 1")
        val course2 = MealCourse("Course 2")
        val course3 = MealCourse("Course 3")
        var availableCourses = listOf(course1, course2)

        // Initial state: Course 1 is GO, Course 2 is GO
        var courseStatuses = mapOf(
            "Course 1" to CourseStatus.GO,
            "Course 2" to CourseStatus.GO
        )

        var state = ProductsScreenState(
            availableCourses = availableCourses,
            courseStatuses = courseStatuses,
            currentActiveCourse = "Course 1"
        )

        // Step 1: Click Go on Course 1 - Course 2 should be set to Go (already Go, but logic applies)
        // Course 1 then becomes Preparing
        courseStatuses = courseStatuses.toMutableMap().apply {
            this["Course 1"] = CourseStatus.PREPARING
            this["Course 2"] = CourseStatus.GO // Explicitly set by Course 1 Go click logic
        }
        state = state.copy(
            courseStatuses = courseStatuses,
            currentActiveCourse = "Course 2" // Course 2 becomes active due to Course 1 Preparing logic
        )

        assertEquals(CourseStatus.PREPARING, state.courseStatuses["Course 1"])
        assertEquals(CourseStatus.GO, state.courseStatuses["Course 2"])
        assertEquals("Course 2", state.currentActiveCourse)

        // Step 2: Add Course 3 while Course 1 is Preparing - Course 3 should be Go and active
        availableCourses = listOf(course1, course2, course3)
        courseStatuses = courseStatuses.toMutableMap().apply {
            this["Course 3"] = CourseStatus.GO // Set because Course 1 (previous course in sequence) is Preparing
        }
        state = state.copy(
            availableCourses = availableCourses,
            courseStatuses = courseStatuses,
            currentActiveCourse = "Course 3" // Course 3 becomes active
        )

        assertEquals(CourseStatus.PREPARING, state.courseStatuses["Course 1"])
        assertEquals(CourseStatus.GO, state.courseStatuses["Course 2"])
        assertEquals(CourseStatus.GO, state.courseStatuses["Course 3"])
        assertEquals("Course 3", state.currentActiveCourse)

        // Step 3: Click Go on Course 2 - Course 1 should become Complete, Course 2 becomes Preparing
        courseStatuses = courseStatuses.toMutableMap().apply {
            this["Course 1"] = CourseStatus.COMPLETE
            this["Course 2"] = CourseStatus.PREPARING
        }
        state = state.copy(courseStatuses = courseStatuses)

        assertEquals(CourseStatus.COMPLETE, state.courseStatuses["Course 1"])
        assertEquals(CourseStatus.PREPARING, state.courseStatuses["Course 2"])
        assertEquals(CourseStatus.GO, state.courseStatuses["Course 3"])

        // Step 4: Click Go on Course 3 - Course 2 should become Complete, Course 3 becomes Preparing
        courseStatuses = courseStatuses.toMutableMap().apply {
            this["Course 2"] = CourseStatus.COMPLETE
            this["Course 3"] = CourseStatus.PREPARING
        }
        state = state.copy(courseStatuses = courseStatuses)

        assertEquals(CourseStatus.COMPLETE, state.courseStatuses["Course 1"])
        assertEquals(CourseStatus.COMPLETE, state.courseStatuses["Course 2"])
        assertEquals(CourseStatus.PREPARING, state.courseStatuses["Course 3"])
    }

    @Test
    fun `test table-based Course 1 Go click sets next course to Go`() {
        // Setup courses
        val course1 = MealCourse("Course 1")
        val course2 = MealCourse("Course 2")
        val availableCourses = listOf(course1, course2)
        val tableId = 101

        // Initial state: Table-specific Course 1 is GO
        val initialTableCourseStatuses = mapOf(
            tableId to mapOf(
                "Course 1" to CourseStatus.GO
            )
        )

        val state = ProductsScreenState(
            tableAvailableCourses = mapOf(tableId to availableCourses),
            tableCourseStatuses = initialTableCourseStatuses,
            tableActiveCourses = mapOf(tableId to "Course 1"),
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = tableId,
                    tableName = "Table 1",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0
        )

        // Simulate clicking Go on Course 1 for table
        // This should set Course 2 to Go status and make it active
        val updatedTableCourseStatuses = initialTableCourseStatuses.toMutableMap()
        val tableStatuses = updatedTableCourseStatuses[tableId]?.toMutableMap() ?: mutableMapOf()
        tableStatuses["Course 2"] = CourseStatus.GO
        updatedTableCourseStatuses[tableId] = tableStatuses

        val updatedTableActiveCourses = mapOf(tableId to "Course 2")

        val updatedState = state.copy(
            tableCourseStatuses = updatedTableCourseStatuses,
            tableActiveCourses = updatedTableActiveCourses
        )

        // Verify the table-specific logic
        assertEquals(CourseStatus.GO, updatedState.tableCourseStatuses[tableId]?.get("Course 1"))
        assertEquals(CourseStatus.GO, updatedState.tableCourseStatuses[tableId]?.get("Course 2"))
        assertEquals("Course 2", updatedState.tableActiveCourses[tableId])
    }

    @Test
    fun `test table-based Course 2 Go click marks Course 1 Complete`() {
        // Setup courses
        val course1 = MealCourse("Course 1")
        val course2 = MealCourse("Course 2")
        val availableCourses = listOf(course1, course2)
        val tableId = 101

        // Initial state: Table-specific Course 1 is PREPARING, Course 2 is GO
        val initialTableCourseStatuses = mapOf(
            tableId to mapOf(
                "Course 1" to CourseStatus.PREPARING,
                "Course 2" to CourseStatus.GO
            )
        )

        val state = ProductsScreenState(
            tableAvailableCourses = mapOf(tableId to availableCourses),
            tableCourseStatuses = initialTableCourseStatuses,
            tableActiveCourses = mapOf(tableId to "Course 2"),
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = tableId,
                    tableName = "Table 1",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0
        )

        // Simulate clicking Go on Course 2 for table
        // This should mark Course 1 as Complete and Course 2 as Preparing
        val updatedTableCourseStatuses = initialTableCourseStatuses.toMutableMap()
        val tableStatuses = updatedTableCourseStatuses[tableId]?.toMutableMap() ?: mutableMapOf()
        tableStatuses["Course 1"] = CourseStatus.COMPLETE
        tableStatuses["Course 2"] = CourseStatus.PREPARING
        updatedTableCourseStatuses[tableId] = tableStatuses

        val updatedState = state.copy(
            tableCourseStatuses = updatedTableCourseStatuses
        )

        // Verify the table-specific logic
        assertEquals(CourseStatus.COMPLETE, updatedState.tableCourseStatuses[tableId]?.get("Course 1"))
        assertEquals(CourseStatus.PREPARING, updatedState.tableCourseStatuses[tableId]?.get("Course 2"))
    }

    @Test
    fun `test mixed table and global course logic isolation`() {
        // Setup courses for both global and table
        val course1 = MealCourse("Course 1")
        val course2 = MealCourse("Course 2")
        val availableCourses = listOf(course1, course2)
        val tableId = 101

        // Initial state: Global Course 1 is GO, Table Course 1 is PREPARING
        val initialGlobalCourseStatuses = mapOf(
            "Course 1" to CourseStatus.GO
        )
        val initialTableCourseStatuses = mapOf(
            tableId to mapOf(
                "Course 1" to CourseStatus.PREPARING,
                "Course 2" to CourseStatus.GO
            )
        )

        val state = ProductsScreenState(
            availableCourses = availableCourses,
            courseStatuses = initialGlobalCourseStatuses,
            currentActiveCourse = "Course 1",
            tableAvailableCourses = mapOf(tableId to availableCourses),
            tableCourseStatuses = initialTableCourseStatuses,
            tableActiveCourses = mapOf(tableId to "Course 2"),
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = tableId,
                    tableName = "Table 1",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0
        )

        // Verify that global and table course statuses are independent
        assertEquals(CourseStatus.GO, state.courseStatuses["Course 1"]) // Global
        assertEquals(CourseStatus.PREPARING, state.tableCourseStatuses[tableId]?.get("Course 1")) // Table
        assertEquals(CourseStatus.GO, state.tableCourseStatuses[tableId]?.get("Course 2")) // Table

        assertEquals("Course 1", state.currentActiveCourse) // Global active
        assertEquals("Course 2", state.tableActiveCourses[tableId]) // Table active
    }

    @Test
    fun `test first course gets Go status on table initialization`() {
        // Setup initial state with no courses for table
        val tableId = 101
        val state = ProductsScreenState(
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = tableId,
                    tableName = "Table 1",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0
        )

        // Simulate table initialization (like initializeTableCourses)
        val defaultCourses = listOf(MealCourse("Course 1"))
        val updatedTableCourseStatuses = mapOf(
            tableId to mapOf("Course 1" to CourseStatus.GO)
        )

        val updatedState = state.copy(
            tableAvailableCourses = mapOf(tableId to defaultCourses),
            tableSelectedCourseForNewItems = mapOf(tableId to "Course 1"),
            tableActiveCourses = mapOf(tableId to "Course 1"),
            tableCourseStatuses = updatedTableCourseStatuses
        )

        // Verify first course has Go status
        assertEquals(CourseStatus.GO, updatedState.tableCourseStatuses[tableId]?.get("Course 1"))
        assertEquals("Course 1", updatedState.tableActiveCourses[tableId])
        assertEquals("Course 1", updatedState.tableSelectedCourseForNewItems[tableId])
    }

    @Test
    fun `test first course gets Go status on first item addition to table`() {
        // Setup initial state with no courses
        val tableId = 101
        val state = ProductsScreenState(
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = tableId,
                    tableName = "Table 1",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0
        )

        // Simulate first course creation when first item is added (like createAndSelectFirstCourse)
        val courseName = "Course 1"
        val newCourse = MealCourse(name = courseName)
        val updatedCourses = listOf(newCourse)

        val updatedTableCourseStatuses = mapOf(
            tableId to mapOf(courseName to CourseStatus.GO)
        )

        val updatedState = state.copy(
            tableAvailableCourses = mapOf(tableId to updatedCourses),
            tableSelectedCourseForNewItems = mapOf(tableId to courseName),
            tableActiveCourses = mapOf(tableId to courseName),
            tableCourseStatuses = updatedTableCourseStatuses
        )

        // Verify first course has Go status
        assertEquals(CourseStatus.GO, updatedState.tableCourseStatuses[tableId]?.get("Course 1"))
        assertEquals("Course 1", updatedState.tableActiveCourses[tableId])
        assertEquals("Course 1", updatedState.tableSelectedCourseForNewItems[tableId])
        assertEquals(1, updatedState.tableAvailableCourses[tableId]?.size)
    }

    @Test
    fun `test first course gets Go status on global course creation`() {
        // Setup initial state with no courses
        val state = ProductsScreenState()

        // Simulate first course creation for global (like createAndSelectFirstCourse)
        val courseName = "Course 1"
        val newCourse = MealCourse(name = courseName)
        val updatedCourses = listOf(newCourse)

        val updatedCourseStatuses = mapOf(courseName to CourseStatus.GO)

        val updatedState = state.copy(
            availableCourses = updatedCourses,
            selectedCourseForNewItems = courseName,
            currentActiveCourse = courseName,
            courseStatuses = updatedCourseStatuses
        )

        // Verify first course has Go status
        assertEquals(CourseStatus.GO, updatedState.courseStatuses["Course 1"])
        assertEquals("Course 1", updatedState.currentActiveCourse)
        assertEquals("Course 1", updatedState.selectedCourseForNewItems)
        assertEquals(1, updatedState.availableCourses.size)
    }

    @Test
    fun `test first numbered course gets Go status for table`() {
        // Setup initial state with no courses for table
        val tableId = 101
        val state = ProductsScreenState(
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = tableId,
                    tableName = "Table 1",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0,
            tableAvailableCourses = mapOf(tableId to emptyList())
        )

        // Simulate adding first numbered course (like addNumberedCourse)
        val courseNumber = 1
        val courseName = "Course $courseNumber"
        val newCourse = MealCourse(name = courseName)
        val updatedCourses = listOf(newCourse)

        val updatedTableCourseStatuses = mapOf(
            tableId to mapOf(courseName to CourseStatus.GO)
        )

        val updatedState = state.copy(
            tableAvailableCourses = mapOf(tableId to updatedCourses),
            tableSelectedCourseForNewItems = mapOf(tableId to courseName),
            tableActiveCourses = mapOf(tableId to courseName),
            tableCourseStatuses = updatedTableCourseStatuses
        )

        // Verify first numbered course has Go status
        assertEquals(CourseStatus.GO, updatedState.tableCourseStatuses[tableId]?.get("Course 1"))
        assertEquals("Course 1", updatedState.tableActiveCourses[tableId])
        assertEquals("Course 1", updatedState.tableSelectedCourseForNewItems[tableId])
    }

    @Test
    fun `test first numbered course gets Go status for global`() {
        // Setup initial state with no courses
        val state = ProductsScreenState(availableCourses = emptyList())

        // Simulate adding first numbered course (like addNumberedCourse)
        val courseNumber = 1
        val courseName = "Course $courseNumber"
        val newCourse = MealCourse(name = courseName)
        val updatedCourses = listOf(newCourse)

        val updatedCourseStatuses = mapOf(courseName to CourseStatus.GO)

        val updatedState = state.copy(
            availableCourses = updatedCourses,
            selectedCourseForNewItems = courseName,
            currentActiveCourse = courseName,
            courseStatuses = updatedCourseStatuses
        )

        // Verify first numbered course has Go status
        assertEquals(CourseStatus.GO, updatedState.courseStatuses["Course 1"])
        assertEquals("Course 1", updatedState.currentActiveCourse)
        assertEquals("Course 1", updatedState.selectedCourseForNewItems)
    }

    @Test
    fun `test subsequent courses follow existing logic`() {
        // Setup initial state with Course 1 already existing and in Preparing status
        val tableId = 101
        val course1 = MealCourse("Course 1")
        val initialCourses = listOf(course1)

        val state = ProductsScreenState(
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = tableId,
                    tableName = "Table 1",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0,
            tableAvailableCourses = mapOf(tableId to initialCourses),
            tableCourseStatuses = mapOf(
                tableId to mapOf("Course 1" to CourseStatus.PREPARING)
            )
        )

        // Simulate adding Course 2 when Course 1 is Preparing
        val course2 = MealCourse("Course 2")
        val updatedCourses = listOf(course1, course2)

        val updatedTableCourseStatuses = mapOf(
            tableId to mapOf(
                "Course 1" to CourseStatus.PREPARING,
                "Course 2" to CourseStatus.GO // Should be Go because Course 1 is Preparing
            )
        )

        val updatedState = state.copy(
            tableAvailableCourses = mapOf(tableId to updatedCourses),
            tableCourseStatuses = updatedTableCourseStatuses,
            tableActiveCourses = mapOf(tableId to "Course 2")
        )

        // Verify Course 1 remains Preparing and Course 2 gets Go status
        assertEquals(CourseStatus.PREPARING, updatedState.tableCourseStatuses[tableId]?.get("Course 1"))
        assertEquals(CourseStatus.GO, updatedState.tableCourseStatuses[tableId]?.get("Course 2"))
        assertEquals("Course 2", updatedState.tableActiveCourses[tableId])
    }

    @Test
    fun `test first course Go status is independent per table`() {
        // Setup multiple tables
        val table1Id = 101
        val table2Id = 102

        val state = ProductsScreenState(
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = table1Id,
                    tableName = "Table 1",
                    tableCapacity = 4
                ),
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = table2Id,
                    tableName = "Table 2",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0
        )

        // Initialize both tables with Course 1
        val course1 = MealCourse("Course 1")
        val courses = listOf(course1)

        val updatedState = state.copy(
            tableAvailableCourses = mapOf(
                table1Id to courses,
                table2Id to courses
            ),
            tableCourseStatuses = mapOf(
                table1Id to mapOf("Course 1" to CourseStatus.GO),
                table2Id to mapOf("Course 1" to CourseStatus.GO)
            ),
            tableActiveCourses = mapOf(
                table1Id to "Course 1",
                table2Id to "Course 1"
            )
        )

        // Verify both tables have independent Course 1 with Go status
        assertEquals(CourseStatus.GO, updatedState.tableCourseStatuses[table1Id]?.get("Course 1"))
        assertEquals(CourseStatus.GO, updatedState.tableCourseStatuses[table2Id]?.get("Course 1"))
        assertEquals("Course 1", updatedState.tableActiveCourses[table1Id])
        assertEquals("Course 1", updatedState.tableActiveCourses[table2Id])
    }

    @Test
    fun `test course + click sets Go status when no courses exist on table`() {
        // Setup table with no existing courses
        val tableId = 101
        val state = ProductsScreenState(
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = tableId,
                    tableName = "Table 1",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0,
            tableAvailableCourses = mapOf(tableId to emptyList()) // No existing courses
        )

        // Simulate adding a new course via course + button (like addNewCourse)
        val courseName = "Starters"
        val newCourse = MealCourse(name = courseName)
        val updatedCourses = listOf(newCourse)

        // Since no courses existed, new course should get Go status
        val updatedTableCourseStatuses = mapOf(
            tableId to mapOf(courseName to CourseStatus.GO)
        )

        val updatedState = state.copy(
            tableAvailableCourses = mapOf(tableId to updatedCourses),
            tableSelectedCourseForNewItems = mapOf(tableId to courseName),
            tableActiveCourses = mapOf(tableId to courseName),
            tableCourseStatuses = updatedTableCourseStatuses
        )

        // Verify the new course has Go status and is active
        assertEquals(CourseStatus.GO, updatedState.tableCourseStatuses[tableId]?.get("Starters"))
        assertEquals("Starters", updatedState.tableActiveCourses[tableId])
        assertEquals("Starters", updatedState.tableSelectedCourseForNewItems[tableId])
        assertEquals(1, updatedState.tableAvailableCourses[tableId]?.size)
    }

    @Test
    fun `test course + click sets Go status when no courses exist globally`() {
        // Setup state with no existing courses
        val state = ProductsScreenState(
            availableCourses = emptyList() // No existing courses
        )

        // Simulate adding a new course via course + button (like addNewCourse)
        val courseName = "Appetizers"
        val newCourse = MealCourse(name = courseName)
        val updatedCourses = listOf(newCourse)

        // Since no courses existed, new course should get Go status
        val updatedCourseStatuses = mapOf(courseName to CourseStatus.GO)

        val updatedState = state.copy(
            availableCourses = updatedCourses,
            selectedCourseForNewItems = courseName,
            currentActiveCourse = courseName,
            courseStatuses = updatedCourseStatuses
        )

        // Verify the new course has Go status and is active
        assertEquals(CourseStatus.GO, updatedState.courseStatuses["Appetizers"])
        assertEquals("Appetizers", updatedState.currentActiveCourse)
        assertEquals("Appetizers", updatedState.selectedCourseForNewItems)
        assertEquals(1, updatedState.availableCourses.size)
    }

    @Test
    fun `test course + click does not set Go status when courses already exist on table`() {
        // Setup table with existing courses
        val tableId = 101
        val existingCourse = MealCourse("Course 1")
        val existingCourses = listOf(existingCourse)

        val state = ProductsScreenState(
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = tableId,
                    tableName = "Table 1",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0,
            tableAvailableCourses = mapOf(tableId to existingCourses),
            tableCourseStatuses = mapOf(
                tableId to mapOf("Course 1" to CourseStatus.PREPARING)
            )
        )

        // Simulate adding a new course via course + button when courses already exist
        val courseName = "Desserts"
        val newCourse = MealCourse(name = courseName)
        val updatedCourses = listOf(existingCourse, newCourse)

        // Since courses already existed, new course should NOT automatically get Go status
        val updatedState = state.copy(
            tableAvailableCourses = mapOf(tableId to updatedCourses),
            tableSelectedCourseForNewItems = mapOf(tableId to courseName)
            // Note: No course status set for the new course
        )

        // Verify the new course does not have explicit Go status (will default to Go when accessed)
        // but the existing course status is preserved
        assertEquals(CourseStatus.PREPARING, updatedState.tableCourseStatuses[tableId]?.get("Course 1"))
        assertNull(updatedState.tableCourseStatuses[tableId]?.get("Desserts")) // Not explicitly set
        assertEquals("Desserts", updatedState.tableSelectedCourseForNewItems[tableId])
        assertEquals(2, updatedState.tableAvailableCourses[tableId]?.size)
    }

    @Test
    fun `test course + click does not set Go status when courses already exist globally`() {
        // Setup state with existing courses
        val existingCourse = MealCourse("Course 1")
        val existingCourses = listOf(existingCourse)

        val state = ProductsScreenState(
            availableCourses = existingCourses,
            courseStatuses = mapOf("Course 1" to CourseStatus.COMPLETE)
        )

        // Simulate adding a new course via course + button when courses already exist
        val courseName = "Beverages"
        val newCourse = MealCourse(name = courseName)
        val updatedCourses = listOf(existingCourse, newCourse)

        // Since courses already existed, new course should NOT automatically get Go status
        val updatedState = state.copy(
            availableCourses = updatedCourses,
            selectedCourseForNewItems = courseName
            // Note: No course status set for the new course
        )

        // Verify the new course does not have explicit Go status
        // but the existing course status is preserved
        assertEquals(CourseStatus.COMPLETE, updatedState.courseStatuses["Course 1"])
        assertNull(updatedState.courseStatuses["Beverages"]) // Not explicitly set
        assertEquals("Beverages", updatedState.selectedCourseForNewItems)
        assertEquals(2, updatedState.availableCourses.size)
    }

    @Test
    fun `test course + click Go status is independent per table`() {
        // Setup multiple tables - one with courses, one without
        val table1Id = 101
        val table2Id = 102

        val existingCourse = MealCourse("Course 1")
        val existingCourses = listOf(existingCourse)

        val state = ProductsScreenState(
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = table1Id,
                    tableName = "Table 1",
                    tableCapacity = 4
                ),
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = table2Id,
                    tableName = "Table 2",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0,
            tableAvailableCourses = mapOf(
                table1Id to existingCourses, // Table 1 has existing courses
                table2Id to emptyList()     // Table 2 has no courses
            ),
            tableCourseStatuses = mapOf(
                table1Id to mapOf("Course 1" to CourseStatus.PREPARING)
            )
        )

        // Add new course to Table 2 (no existing courses)
        val courseName = "Mains"
        val newCourse = MealCourse(name = courseName)

        val updatedState = state.copy(
            tableAvailableCourses = mapOf(
                table1Id to existingCourses,
                table2Id to listOf(newCourse)
            ),
            tableCourseStatuses = mapOf(
                table1Id to mapOf("Course 1" to CourseStatus.PREPARING),
                table2Id to mapOf(courseName to CourseStatus.GO) // New course gets Go status
            ),
            tableActiveCourses = mapOf(
                table2Id to courseName // New course becomes active
            )
        )

        // Verify Table 1 is unaffected and Table 2 gets Go status for new course
        assertEquals(CourseStatus.PREPARING, updatedState.tableCourseStatuses[table1Id]?.get("Course 1"))
        assertEquals(CourseStatus.GO, updatedState.tableCourseStatuses[table2Id]?.get("Mains"))
        assertEquals("Mains", updatedState.tableActiveCourses[table2Id])
        assertNull(updatedState.tableActiveCourses[table1Id]) // No change
    }

    @Test
    fun `test table-based Course 1 Go click sets Course 2 to Go`() {
        // Setup table with Course 1 and Course 2
        val tableId = 101
        val course1 = MealCourse("Course 1")
        val course2 = MealCourse("Course 2")
        val availableCourses = listOf(course1, course2)

        // Initial state: Course 1 is GO, Course 2 exists but no status set
        val initialTableCourseStatuses = mapOf(
            tableId to mapOf(
                "Course 1" to CourseStatus.GO
            )
        )

        val state = ProductsScreenState(
            tableAvailableCourses = mapOf(tableId to availableCourses),
            tableCourseStatuses = initialTableCourseStatuses,
            tableActiveCourses = mapOf(tableId to "Course 1"),
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = tableId,
                    tableName = "Table 1",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0
        )

        // Simulate clicking Go on Course 1 for table
        // This should set Course 2 to Go (Course 1 remains Go until it becomes Preparing)
        val updatedTableCourseStatuses = mapOf(
            tableId to mapOf(
                "Course 1" to CourseStatus.GO, // Remains Go
                "Course 2" to CourseStatus.GO
            )
        )

        val updatedState = state.copy(
            tableCourseStatuses = updatedTableCourseStatuses,
            tableActiveCourses = mapOf(tableId to "Course 2") // Course 2 becomes active
        )

        // Verify the table-based Course 1 Go click logic
        assertEquals(CourseStatus.GO, updatedState.tableCourseStatuses[tableId]?.get("Course 1"))
        assertEquals(CourseStatus.GO, updatedState.tableCourseStatuses[tableId]?.get("Course 2"))
        assertEquals("Course 2", updatedState.tableActiveCourses[tableId])
    }

    @Test
    fun `test table-based Course 1 Go click with no Course 2`() {
        // Setup table with only Course 1
        val tableId = 101
        val course1 = MealCourse("Course 1")
        val availableCourses = listOf(course1)

        // Initial state: Only Course 1 exists and is GO
        val initialTableCourseStatuses = mapOf(
            tableId to mapOf(
                "Course 1" to CourseStatus.GO
            )
        )

        val state = ProductsScreenState(
            tableAvailableCourses = mapOf(tableId to availableCourses),
            tableCourseStatuses = initialTableCourseStatuses,
            tableActiveCourses = mapOf(tableId to "Course 1"),
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = tableId,
                    tableName = "Table 1",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0
        )

        // Simulate clicking Go on Course 1 when no Course 2 exists
        // This should not change Course 1 status (no next course to set to Go)
        val updatedState = state.copy()

        // Verify Course 1 remains Go and no Course 2 is created
        assertEquals(CourseStatus.GO, updatedState.tableCourseStatuses[tableId]?.get("Course 1"))
        assertNull(updatedState.tableCourseStatuses[tableId]?.get("Course 2"))
        assertEquals(1, updatedState.tableAvailableCourses[tableId]?.size)
        assertEquals("Course 1", updatedState.tableActiveCourses[tableId])
    }

    @Test
    fun `test global Course 1 Go click maintains existing behavior`() {
        // Setup global courses (no table selected)
        val course1 = MealCourse("Course 1")
        val course2 = MealCourse("Course 2")
        val availableCourses = listOf(course1, course2)

        // Initial state: Course 1 is GO, Course 2 exists but no status set
        val initialCourseStatuses = mapOf(
            "Course 1" to CourseStatus.GO
        )

        val state = ProductsScreenState(
            availableCourses = availableCourses,
            courseStatuses = initialCourseStatuses,
            currentActiveCourse = "Course 1"
        )

        // Simulate clicking Go on Course 1 for global (no table)
        // This should set Course 2 to Go (existing behavior, NOT mark Course 1 Complete)
        val updatedCourseStatuses = mapOf(
            "Course 1" to CourseStatus.GO, // Remains Go (existing behavior)
            "Course 2" to CourseStatus.GO
        )

        val updatedState = state.copy(
            courseStatuses = updatedCourseStatuses,
            currentActiveCourse = "Course 2" // Course 2 becomes active
        )

        // Verify global Course 1 Go click maintains existing behavior
        assertEquals(CourseStatus.GO, updatedState.courseStatuses["Course 1"]) // Still Go, not Complete
        assertEquals(CourseStatus.GO, updatedState.courseStatuses["Course 2"])
        assertEquals("Course 2", updatedState.currentActiveCourse)
    }

    @Test
    fun `test table vs global Course 1 Go click behavior same`() {
        // Setup both table and global scenarios
        val course1 = MealCourse("Course 1")
        val course2 = MealCourse("Course 2")
        val availableCourses = listOf(course1, course2)
        val tableId = 101

        val state = ProductsScreenState(
            // Global courses
            availableCourses = availableCourses,
            courseStatuses = mapOf("Course 1" to CourseStatus.GO),
            currentActiveCourse = "Course 1",

            // Table courses
            tableAvailableCourses = mapOf(tableId to availableCourses),
            tableCourseStatuses = mapOf(
                tableId to mapOf("Course 1" to CourseStatus.GO)
            ),
            tableActiveCourses = mapOf(tableId to "Course 1"),
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = tableId,
                    tableName = "Table 1",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0
        )

        // Simulate table-based Course 1 Go click
        val tableUpdatedState = state.copy(
            tableCourseStatuses = mapOf(
                tableId to mapOf(
                    "Course 1" to CourseStatus.GO, // Table: Course 1 remains Go
                    "Course 2" to CourseStatus.GO
                )
            ),
            tableActiveCourses = mapOf(tableId to "Course 2")
        )

        // Simulate global Course 1 Go click
        val globalUpdatedState = state.copy(
            courseStatuses = mapOf(
                "Course 1" to CourseStatus.GO, // Global: Course 1 remains Go
                "Course 2" to CourseStatus.GO
            ),
            currentActiveCourse = "Course 2"
        )

        // Verify same behaviors for both table and global
        // Table-based: Course 1 remains Go, Course 2 becomes Go
        assertEquals(CourseStatus.GO, tableUpdatedState.tableCourseStatuses[tableId]?.get("Course 1"))
        assertEquals(CourseStatus.GO, tableUpdatedState.tableCourseStatuses[tableId]?.get("Course 2"))
        assertEquals("Course 2", tableUpdatedState.tableActiveCourses[tableId])

        // Global: Course 1 remains Go, Course 2 becomes Go
        assertEquals(CourseStatus.GO, globalUpdatedState.courseStatuses["Course 1"])
        assertEquals(CourseStatus.GO, globalUpdatedState.courseStatuses["Course 2"])
        assertEquals("Course 2", globalUpdatedState.currentActiveCourse)
    }

    @Test
    fun `test table-based Course 1 Go and Course 2 Go flow integration`() {
        // Setup table with Course 1, Course 2, and Course 3
        val tableId = 101
        val course1 = MealCourse("Course 1")
        val course2 = MealCourse("Course 2")
        val course3 = MealCourse("Course 3")
        val availableCourses = listOf(course1, course2, course3)

        // Initial state: Course 1 is GO
        var state = ProductsScreenState(
            tableAvailableCourses = mapOf(tableId to availableCourses),
            tableCourseStatuses = mapOf(
                tableId to mapOf("Course 1" to CourseStatus.GO)
            ),
            tableActiveCourses = mapOf(tableId to "Course 1"),
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    areaId = 1,
                    areaName = "Main Area",
                    tableId = tableId,
                    tableName = "Table 1",
                    tableCapacity = 4
                )
            ),
            selectedTableIndex = 0
        )

        // Step 1: Click Go on Course 1 - Course 1 remains Go, Course 2 becomes Go
        state = state.copy(
            tableCourseStatuses = mapOf(
                tableId to mapOf(
                    "Course 1" to CourseStatus.GO,
                    "Course 2" to CourseStatus.GO
                )
            ),
            tableActiveCourses = mapOf(tableId to "Course 2")
        )

        assertEquals(CourseStatus.GO, state.tableCourseStatuses[tableId]?.get("Course 1"))
        assertEquals(CourseStatus.GO, state.tableCourseStatuses[tableId]?.get("Course 2"))
        assertEquals("Course 2", state.tableActiveCourses[tableId])

        // Step 2: Click Go on Course 2 - Course 1 becomes Complete, Course 2 becomes Preparing
        state = state.copy(
            tableCourseStatuses = mapOf(
                tableId to mapOf(
                    "Course 1" to CourseStatus.COMPLETE,
                    "Course 2" to CourseStatus.PREPARING
                )
            )
        )

        assertEquals(CourseStatus.COMPLETE, state.tableCourseStatuses[tableId]?.get("Course 1"))
        assertEquals(CourseStatus.PREPARING, state.tableCourseStatuses[tableId]?.get("Course 2"))

        // Step 3: Course 2 is Preparing, so Course 3 should become Go and active (existing logic)
        state = state.copy(
            tableCourseStatuses = mapOf(
                tableId to mapOf(
                    "Course 1" to CourseStatus.COMPLETE,
                    "Course 2" to CourseStatus.PREPARING,
                    "Course 3" to CourseStatus.GO
                )
            ),
            tableActiveCourses = mapOf(tableId to "Course 3")
        )

        assertEquals(CourseStatus.COMPLETE, state.tableCourseStatuses[tableId]?.get("Course 1"))
        assertEquals(CourseStatus.PREPARING, state.tableCourseStatuses[tableId]?.get("Course 2"))
        assertEquals(CourseStatus.GO, state.tableCourseStatuses[tableId]?.get("Course 3"))
        assertEquals("Course 3", state.tableActiveCourses[tableId])
    }
}
