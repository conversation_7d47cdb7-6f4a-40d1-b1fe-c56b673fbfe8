package com.thedasagroup.suminative.data.model.request.table_sync

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Request model for syncing order to table
 * Matches the CURL request structure for POST /api/tables/sync/order
 */
@Serializable
data class SyncOrderRequest(
    @SerialName("tableId")
    val tableId: Int,
    @SerialName("customerId")
    val customerId: Int,
    @SerialName("businessId")
    val businessId: Int,
    @SerialName("netPayable")
    val netPayable: Double,
    @SerialName("orderCourses")
    val orderCourses: List<OrderCourse>
)

/**
 * Order course data model for sync request
 */
@Serializable
data class OrderCourse(
    @SerialName("coursesName")
    val coursesName: String,
    @SerialName("cartJson")
    val cartJson: String
)
