package com.thedasagroup.suminative.ui.table_sync

import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.model.request.table_sync.OrderCourse
import com.thedasagroup.suminative.data.model.request.table_sync.SyncOrderRequest
import com.thedasagroup.suminative.data.model.request.table_sync.UpdateOrderRequest
import com.thedasagroup.suminative.domain.table_sync.GetSyncedOrderForTableUseCase
import com.thedasagroup.suminative.domain.table_sync.SyncOrderToTableUseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Example usage of Table Sync APIs
 * This demonstrates how to use the implemented sync order functionality
 */
class TableSyncUsageExample {

    /**
     * Example of syncing an order to a table
     */
    fun syncOrderExample(viewModel: TableSyncViewModel) {
        // Create order courses with cart JSON
        val orderCourses = listOf(
            OrderCourse(
                coursesName = "Starters",
                cartJson = "{\"items\":[{\"id\":101,\"name\":\"Soup\",\"qty\":1,\"price\":5.5}]}"
            ),
            OrderCourse(
                coursesName = "Mains",
                cartJson = "{\"items\":[{\"id\":202,\"name\":\"Burger\",\"qty\":2,\"price\":12.0}]}"
            )
        )

        // Create sync order request
        val syncRequest = SyncOrderRequest(
            tableId = 47,
            customerId = 345,
            businessId = 104,
            netPayable = 47.5,
            orderCourses = orderCourses
        )

        // Sync order to table using ViewModel
        CoroutineScope(Dispatchers.Main).launch {
            val result = viewModel.syncOrderToTable(syncRequest)
            result.collect { asyncResult ->
                when (asyncResult) {
                    is Success -> {
                        val response = asyncResult()
                        if (response.success) {
                            println("Order synced successfully: ${response.message}")
                            println("Order ID: ${response.data?.id}")
                            println("Table ID: ${response.data?.tableId}")
                        } else {
                            println("Sync failed: ${response.message}")
                        }
                    }
                    else -> {
                        println("Sync operation failed or loading")
                    }
                }
            }
        }
    }

    /**
     * Example of getting synced order for a table
     */
    fun getSyncedOrderExample(viewModel: TableSyncViewModel) {
        val tableId = 47

        // Get synced order for table using ViewModel
        CoroutineScope(Dispatchers.Main).launch {
            val result = viewModel.getSyncedOrderForTable(tableId)
            result.collect { asyncResult ->
                when (asyncResult) {
                    is Success -> {
                        val response = asyncResult()
                        if (response.success) {
                            println("Order fetched successfully: ${response.message}")
                            println("Order ID: ${response.data?.id}")
                            println("Table ID: ${response.data?.tableId}")
                            println("Net Payable: ${response.data?.netPayable}")
                            println("Customer ID: ${response.data?.customerId}")
                            
                            // Print order courses
                            response.data?.orderCourses?.forEach { course ->
                                println("Course: ${course.coursesName}")
                                println("Cart JSON: ${course.cartJson}")
                            }
                        } else {
                            println("Fetch failed: ${response.message}")
                        }
                    }
                    else -> {
                        println("Fetch operation failed or loading")
                    }
                }
            }
        }
    }

    /**
     * Example of using the APIs directly with use cases (without ViewModel)
     */
    fun directUseCaseExample(
        syncOrderToTableUseCase: SyncOrderToTableUseCase,
        getSyncedOrderForTableUseCase: GetSyncedOrderForTableUseCase
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            // Example 1: Sync order to table
            val syncRequest = SyncOrderRequest(
                tableId = 47,
                customerId = 345,
                businessId = 104,
                netPayable = 47.5,
                orderCourses = listOf(
                    OrderCourse(
                        coursesName = "Starters",
                        cartJson = "{\"items\":[{\"id\":101,\"name\":\"Soup\",\"qty\":1,\"price\":5.5}]}"
                    )
                )
            )

            val syncResult = syncOrderToTableUseCase(syncRequest)
            syncResult.collect { asyncResult ->
                when (asyncResult) {
                    is Success -> {
                        println("Direct sync successful: ${asyncResult().message}")
                    }
                    else -> {
                        println("Direct sync failed")
                    }
                }
            }

            // Example 2: Get synced order
            val getResult = getSyncedOrderForTableUseCase(47)
            getResult.collect { asyncResult ->
                when (asyncResult) {
                    is Success -> {
                        println("Direct get successful: ${asyncResult().message}")
                    }
                    else -> {
                        println("Direct get failed")
                    }
                }
            }
        }
    }

    /**
     * Example of toggling table occupied status
     */
    fun toggleTableOccupiedExample(viewModel: TableSyncViewModel) {
        val tableId = 47
        val netPayable = 47.5

        // Toggle table occupied status using ViewModel
        CoroutineScope(Dispatchers.Main).launch {
            val result = viewModel.toggleTableOccupied(tableId, netPayable)
            result.collect { asyncResult ->
                when (asyncResult) {
                    is Success -> {
                        val response = asyncResult()
                        println("Table toggle successful")
                        println("Table ID: ${response.id}")
                        println("Table Name: ${response.tableName}")
                        println("Occupied: ${response.occupied}")
                        println("Net Payable: ${response.netPayable}")
                    }
                    else -> {
                        println("Toggle operation failed or loading")
                    }
                }
            }
        }
    }

    /**
     * Example of updating order for a table
     */
    fun updateOrderExample(viewModel: TableSyncViewModel) {
        val tableId = 47

        // Create update order request
        val updateRequest = UpdateOrderRequest(
            tableId = 12,
            customerId = 345,
            businessId = 104,
            netPayable = 47.5,
            orderCourses = listOf(
                OrderCourse(
                    coursesName = "Starters",
                    cartJson = "{\"items\":[{\"id\":101,\"name\":\"Soup\",\"qty\":1,\"price\":5.5}]}"
                ),
                OrderCourse(
                    coursesName = "Mains",
                    cartJson = "{\"items\":[{\"id\":202,\"name\":\"Burger\",\"qty\":2,\"price\":12.0}]}"
                )
            )
        )

        // Update order for table using ViewModel
        CoroutineScope(Dispatchers.Main).launch {
            val result = viewModel.updateOrderForTable(tableId, updateRequest)
            result.collect { asyncResult ->
                when (asyncResult) {
                    is Success -> {
                        val response = asyncResult()
                        if (response.success) {
                            println("Order updated successfully: ${response.message}")
                            println("Order ID: ${response.data?.id}")
                            println("Table ID: ${response.data?.tableId}")
                        } else {
                            println("Update failed: ${response.message}")
                        }
                    }
                    else -> {
                        println("Update operation failed or loading")
                    }
                }
            }
        }
    }

    /**
     * Example of deleting order for a table
     */
    fun deleteOrderExample(viewModel: TableSyncViewModel) {
        val tableId = 47

        // Delete order for table using ViewModel
        CoroutineScope(Dispatchers.Main).launch {
            val result = viewModel.deleteOrderForTable(tableId)
            result.collect { asyncResult ->
                when (asyncResult) {
                    is Success -> {
                        val response = asyncResult()
                        if (response.success) {
                            println("Order deleted successfully: ${response.message}")
                        } else {
                            println("Delete failed: ${response.message}")
                        }
                    }
                    else -> {
                        println("Delete operation failed or loading")
                    }
                }
            }
        }
    }
}
