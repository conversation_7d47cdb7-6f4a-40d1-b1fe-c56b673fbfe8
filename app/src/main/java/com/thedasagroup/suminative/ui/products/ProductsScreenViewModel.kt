package com.thedasagroup.suminative.ui.products

import android.util.Log
import androidx.lifecycle.viewModelScope
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.airbnb.mvrx.mocking.MockableMavericks
import com.airbnb.mvrx.mocking.MockableMavericks.setState
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.model.request.cloud_print.CloudPrintRequest
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.OptionSet
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.data.model.request.order.encode
import com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem2
import com.thedasagroup.suminative.data.model.request.print.PrintBillRequest
import com.thedasagroup.suminative.data.model.request.print.SendToKitchenItem
import com.thedasagroup.suminative.data.model.request.print.SendToKitchenRequest
import com.thedasagroup.suminative.data.model.request.sales.SalesRequest
import com.thedasagroup.suminative.data.model.response.courses_notification.CoursesNotificationResponse
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.data.model.response.order.OrderResponse2
import com.thedasagroup.suminative.data.model.response.sales.SalesReportResponse
import com.thedasagroup.suminative.data.model.response.sales.SalesResponse
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse
import com.thedasagroup.suminative.data.model.response.store_orders.Option
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.PrintRepository
import com.thedasagroup.suminative.data.repo.ProductRepository
import com.thedasagroup.suminative.domain.courses_notification.SendCoursesNotificationUseCase
import com.thedasagroup.suminative.domain.cloud_print.CloudPrintUseCase
import com.thedasagroup.suminative.domain.orders.CreateOrderUseCase
import com.thedasagroup.suminative.domain.sales_report.GetSalesReportUseCase
import com.thedasagroup.suminative.ui.sales.TotalSalesUseCase
import com.thedasagroup.suminative.ui.stock.StockUseCase
import com.thedasagroup.suminative.ui.utils.transformDecimal
import com.thedasagroup.suminative.ui.products.cart.CourseStatus
import com.thedasagroup.suminative.work.OrderSyncManager
import com.thedasagroup.suminative.work.SyncStatus
import com.thedasagroup.suminative.ui.table_sync.TableSyncViewModel
import com.thedasagroup.suminative.data.model.request.table_sync.OrderCourse
import com.thedasagroup.suminative.data.model.request.table_sync.SyncOrderRequest
import com.thedasagroup.suminative.data.model.request.table_sync.UpdateOrderRequest
import com.thedasagroup.suminative.domain.table_sync.GetSyncedOrderForTableUseCase
import com.thedasagroup.suminative.domain.table_sync.SyncOrderToTableUseCase
import com.thedasagroup.suminative.domain.table_sync.ToggleTableOccupiedUseCase
import com.thedasagroup.suminative.domain.table_sync.UpdateOrderForTableUseCase
import com.thedasagroup.suminative.domain.table_sync.DeleteOrderForTableUseCase
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import kotlin.collections.set

// Data class for Meal Course
data class MealCourse(
    val name: String
)

// Extended Cart item with meal course assignment
data class CartItemWithCourse(
    val cart: Cart,
    val courseId: String = "" // No default course initially
)

// Filter options for viewing courses
enum class CourseFilter(val displayName: String) {
    ALL("All"),
    STARTERS("Starters"),
    MAINS("Mains"),
    DESSERTS("Desserts")
}

class ProductsScreenViewModel @AssistedInject constructor(
    @Assisted state: ProductsScreenState,
    val prefs: Prefs,
    val stockUseCase: StockUseCase,
    val orderUseCase: PlaceOnlineOrderUseCase,
    val offlineOrderUseCase: OrderUseCase,
    val cloudPrintUseCase: CloudPrintUseCase,
    val getOptionDetailsUseCase: OptionDetailsUseCase,
    val salesUseCase: TotalSalesUseCase,
    val salesReportUseCase: GetSalesReportUseCase,
    val trueTimeImpl: TrueTimeImpl,
    val productsRepository: ProductRepository,
    val downloadProductsUseCase: DownloadProductsUseCase,
    val orderSyncManager: OrderSyncManager,
    val sendCoursesNotificationUseCase: SendCoursesNotificationUseCase,
    val printRepository: PrintRepository,
    val getSyncedOrderForTableUseCase: GetSyncedOrderForTableUseCase,
    val syncOrderToTableUseCase: SyncOrderToTableUseCase,
    val toggleTableOccupiedUseCase: ToggleTableOccupiedUseCase,
    val updateOrderForTableUseCase: UpdateOrderForTableUseCase,
    val deleteOrderForTableUseCase: DeleteOrderForTableUseCase
) : MavericksViewModel<ProductsScreenState>(state) {

    init {
        monitorSyncStatus()
    }

    suspend fun getStockItems(): StateFlow<Async<StockItemsResponse>> {
        val flow = MutableStateFlow<Async<StockItemsResponse>>(Loading())
        setState {
            copy(stockItemsResponse = Loading())
        }
        stockUseCase().execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(stockItemsResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(stockItemsResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    suspend fun refreshProducts(): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Loading())
        setState {
            copy(refreshing = true)
        }

        downloadProductsUseCase.refreshProducts().execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    viewModelScope.launch {
                        getStockItems()
                    }
                    // Refresh the stock items after successful product refresh
                    copy(refreshing = false)
                }

                else -> {
                    flow.value = Uninitialized
                    copy(refreshing = false)
                }
            }
        }
        return flow
    }

    suspend fun getTotalSales(request: SalesRequest): StateFlow<Async<SalesResponse>> {
        val flow = MutableStateFlow<Async<SalesResponse>>(Loading())
        setState {
            copy(salesResponse = Loading())
        }
        salesUseCase(request = request).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(salesResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(salesResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    suspend fun getSalesReport(request: SalesRequest): StateFlow<Async<SalesReportResponse>> {
        val flow = MutableStateFlow<Async<SalesReportResponse>>(Loading())
        setState {
            copy(salesReportResponse = Loading(), salesRequest = request)
        }
        salesReportUseCase(request = request).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(salesReportResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(salesReportResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    fun updateStock(
        order: Order, stock: Int, optionDetails: OptionDetails, stockItem: StoreItem,
        tableOrders: Map<Int, Order>,
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>
    ) {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = selectedTableIndex,
            selectedTables = selectedTables
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order // Fallback to passed order if no table selected
        }

        val updatedStock = stockItem.copy(quantity = stock)
        val updatedOrder = targetOrder.carts?.map {
            // Use UUID to identify the specific cart item (this method is used for product details updates)
            // For now, keep the existing logic since this is used from product details screen
            if (it.storeItem?.id == stockItem.id &&
                areOptionSetsEqual(it.storeItem?.optionSets, stockItem.optionSets)
            ) {
                it.copy(storeItem = updatedStock)
            } else {
                it
            }
        }

        val finalOrder = targetOrder.copy(carts = updatedOrder)

        setState {
            if (currentTableId != null) {
                // Update table-specific cart
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = finalOrder
                copy(
                    stock = stock,
                    tableOrders = updatedTableOrders,
                    productTotal = calculateTotal(
                        stockItem = stockItem, optionDetails = optionDetails, updatedStock = stock
                    ).billAmount ?: 0.0
                )
            } else {
                // Fallback to global cart
                copy(
                    stock = stock,
                    order = finalOrder,
                    productTotal = calculateTotal(
                        stockItem = stockItem, optionDetails = optionDetails, updatedStock = stock
                    ).billAmount ?: 0.0
                )
            }
        }
    }


    fun resetStock() {
        setState {
            copy(
                stock = 1,
                optionDetailsResponse = Uninitialized,
                productTotal = 0.0
            )
        }
    }


    fun updateCartStock(
        state: ProductsScreenState,
        order: Order, stock: Int, stockItem: StoreItem, optionDetails: OptionDetails,
        tableOrders: Map<Int, Order>,
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        cart: Cart? = null
    ) {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = selectedTableIndex,
            selectedTables = selectedTables
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order // Fallback to passed order if no table selected
        }

        val updatedStock = stockItem.copy(quantity = stock)
        val updatedOrder = targetOrder.carts?.map {
            // Use cart UUID if available, otherwise fall back to store item ID and options
            val isTargetItem = if (cart != null) {
                it.uuid == cart.uuid
            } else {
                it.storeItem?.id == stockItem.id &&
                        areOptionSetsEqual(it.storeItem?.optionSets, stockItem.optionSets)
            }

            if (isTargetItem) {
                // Update the item-level totals based on new quantity
                val unitPrice = (it.netPayable ?: 0.0) / (it.quantity ?: 1)
                val unitTax = (it.tax ?: 0.0) / (it.quantity ?: 1)
                val newNetPayable = unitPrice * stock
                val newTax = unitTax * stock

                it.copy(
                    quantity = stock,
                    netPayable = newNetPayable,
                    tax = newTax,
                    storeItem = updatedStock
                )
            } else {
                it
            }
        }

        // Recalculate order totals
        val netPayable = updatedOrder?.sumByDouble { it.netPayable ?: 0.0 } ?: 0.0
        val totalTax = updatedOrder?.sumByDouble { it.tax ?: 0.0 } ?: 0.0

        // Check if service charge is applied and include it in total
        val serviceChargeApplied = if (currentTableId != null) {
            state.tableServiceChargeApplied[currentTableId] ?: false
        } else {
            state.serviceChargeApplied
        }
        val serviceChargeAmount = if (serviceChargeApplied) {
            netPayable * (getServiceChargePercentage() / 100.0)
        } else {
            0.0
        }
        val totalPrice = netPayable + totalTax + serviceChargeAmount

        val finalOrder = targetOrder.copy(
            carts = updatedOrder,
            netPayable = netPayable,
            tax = totalTax,
            totalPrice = totalPrice
        )

        // Ensure at least one course exists before assigning cart items
        val stateWithCourse = state

        setState {

            if (currentTableId != null) {
                // Update table-specific cart
                val updatedTableOrders = stateWithCourse.tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = finalOrder

                // Table-specific sync
                val currentOrder = finalOrder
                val existingAssignments =
                    stateWithCourse.cartItemsWithCourses[currentTableId]?.associateBy { it.cart.uuid }
                        ?: emptyMap()

                val updatedCartItems = currentOrder.carts?.map { cart ->
                    existingAssignments[cart.uuid] ?: CartItemWithCourse(
                        cart = cart,
                        courseId = cart.storeItem?.courseId
                            ?: stateWithCourse.getCurrentTableSelectedCourseForNewItems().ifEmpty {
                                stateWithCourse.getCurrentTableAvailableCourses()
                                    .firstOrNull()?.name
                                    ?: ""
                            }
                    )
                } ?: emptyList()

                val updatedCartItemsWithCourses =
                    stateWithCourse.cartItemsWithCourses.toMutableMap()
                updatedCartItemsWithCourses[currentTableId] = updatedCartItems

                stateWithCourse.copy(
                    tableOrders = updatedTableOrders,
                    productTotal = calculateTotal(
                        stockItem = stockItem, optionDetails = optionDetails, updatedStock = stock
                    ).billAmount ?: 0.0,
                    stock = stock,
                    cartItemsWithCourses = updatedCartItemsWithCourses
                )

            } else {

                // Global sync when no tables
                val currentOrder = finalOrder
                val existingAssignments =
                    stateWithCourse.globalCartItemsWithCourses.associateBy { it.cart.storeItem?.id }

                val updatedGlobalCartItems = currentOrder.carts?.map { cart ->
                    existingAssignments[cart.storeItem?.id] ?: CartItemWithCourse(
                        cart = cart,
                        courseId = stateWithCourse.getCurrentTableSelectedCourseForNewItems()
                            .ifEmpty {
                                stateWithCourse.getCurrentTableAvailableCourses()
                                    .firstOrNull()?.name
                                    ?: ""
                            }
                    )
                } ?: emptyList()

                stateWithCourse.copy(
                    order = finalOrder,
                    productTotal = calculateTotal(
                        stockItem = stockItem, optionDetails = optionDetails, updatedStock = stock
                    ).billAmount ?: 0.0,
                    stock = stock,
                    globalCartItemsWithCourses = updatedGlobalCartItems
                )
            }
        }

        // Sync course assignments after updating cart stock
        syncCartItemsWithCourses(state = stateWithCourse)
    }

    fun updateCartItemNotes(
        order: Order, cart: Cart, notes: String,
        tableOrders: Map<Int, Order>,
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>
    ) {
        val currentTableId = getCurrentTableId(
            selectedTableIndex = selectedTableIndex,
            selectedTables = selectedTables
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order
        }

        val updatedOrder = targetOrder.carts?.map {
            // Use UUID to identify the specific cart item
            if (it.uuid == cart.uuid) {
                it.copy(notes = notes)
            } else {
                it
            }
        }

        val finalOrder = targetOrder.copy(carts = updatedOrder)

        setState {
            if (currentTableId != null) {
                // Update table-specific cart
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = finalOrder
                copy(tableOrders = updatedTableOrders)
            } else {
                // Fallback to global cart
                copy(order = finalOrder)
            }
        }
    }

    fun voidCartItem(
        order: Order, cart: Cart,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        selectedTableIndex: Int,
        tableOrders: Map<Int, Order>
    ) {
        val currentTableId = getCurrentTableId(
            selectedTables = selectedTables,
            selectedTableIndex = selectedTableIndex
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order // Fallback to passed order if no table selected
        }
        val updatedOrder = targetOrder.carts?.map {
            // Use UUID to identify the specific cart item
            if (it.uuid == cart.uuid) {
                // Create a voided version of the cart item with all prices set to 0 (keep quantity unchanged)
                val voidedStoreItem = it.storeItem?.copy(
                    price = 0.0,
                    billAmount = 0.0,
                    tax = 0.0,
                    discountedAmount = 0.0,
                    optionSets = it.storeItem.optionSets?.map { optionSet ->
                        optionSet.copy(
                            options = optionSet.options.map { option ->
                                option?.copy(price = 0.0)
                            } ?: mutableListOf()
                        )
                    }
                )

                it.copy(
                    storeItem = voidedStoreItem,
                    price = 0.0,
                    netPayable = 0.0,
                    tax = 0.0,
                    extraPrice = 0.0,
                    optionPrice = 0.0,
                    discount = 0.0,
                    notes = (it.notes
                        ?: "") + if (it.notes.isNullOrEmpty()) "VOIDED" else " - VOIDED"
                )
            } else {
                it
            }
        }

        // Recalculate order totals
        val netPayable = updatedOrder?.sumOf { it.netPayable ?: 0.0 } ?: 0.0
        val totalTax = updatedOrder?.sumOf { it.tax ?: 0.0 } ?: 0.0

        setState {
            // Check if service charge is applied and include it in total
            val isServiceChargeApplied = if (currentTableId != null) {
                tableServiceChargeApplied[currentTableId] ?: false
            } else {
                serviceChargeApplied
            }
            val serviceChargeAmount = if (isServiceChargeApplied) {
                netPayable * (getServiceChargePercentage() / 100.0)
            } else {
                0.0
            }
            val totalPrice = netPayable + totalTax + serviceChargeAmount
            if (currentTableId != null) {
                val finalOrder = order.copy(
                    carts = updatedOrder,
                    netPayable = netPayable,
                    tax = totalTax,
                    totalPrice = totalPrice
                )
                // Update table-specific cart
                val updatedTableOrders = tableOrders.toMutableMap()
                updatedTableOrders[currentTableId] = finalOrder

                val currentOrder = finalOrder
                val existingAssignments =
                    cartItemsWithCourses[currentTableId]?.associateBy { it.cart.storeItem?.id }
                        ?: emptyMap()

                val updatedCartItems = currentOrder.carts?.map { cart ->
                    val course = existingAssignments[cart.storeItem?.id] ?: CartItemWithCourse(
                        cart = cart,
                        courseId = cart.storeItem?.courseId
                            ?: if (getCurrentTableSelectedCourseForNewItems().isNotEmpty()) getCurrentTableSelectedCourseForNewItems() else getCurrentTableAvailableCourses().firstOrNull()?.name
                                ?: ""
                    )
                    course.copy(cart = cart)
                } ?: emptyList()

                val updatedCartItemsWithCourses = cartItemsWithCourses.toMutableMap()
                updatedCartItemsWithCourses[currentTableId] = updatedCartItems

                copy(
                    tableOrders = updatedTableOrders,
                    order = order.copy(
                        carts = updatedOrder,
                        netPayable = netPayable,
                        tax = totalTax,
                        totalPrice = totalPrice
                    ),
                    cartItemsWithCourses = updatedCartItemsWithCourses
                )
            } else {

                val currentOrder = order.copy(
                    carts = updatedOrder,
                    netPayable = netPayable,
                    tax = totalTax,
                    totalPrice = totalPrice
                )
                val existingAssignments =
                    globalCartItemsWithCourses.associateBy { it.cart.storeItem?.id }

                val updatedGlobalCartItems = currentOrder.carts?.map { cart ->
                    val course = existingAssignments[cart.storeItem?.id] ?: CartItemWithCourse(
                        cart = cart,
                        courseId = cart.storeItem?.courseId
                            ?: if (getCurrentTableSelectedCourseForNewItems().isNotEmpty()) getCurrentTableSelectedCourseForNewItems() else getCurrentTableAvailableCourses().firstOrNull()?.name
                                ?: ""
                    )
                    course.copy(cart = cart)
                } ?: emptyList()

                // Fallback to global cart
                copy(
                    order = currentOrder,
                    globalCartItemsWithCourses = updatedGlobalCartItems,
                )
            }
        }
    }

    /**
     * Save table order when first item is added to cart
     */
    private suspend fun saveTableOrderIfNeeded(
        tableId: Int,
        order: Order,
        state: ProductsScreenState
    ) {
        try {
            val currentOrder = state.tableOrders[tableId]
            val wasEmpty = currentOrder?.carts?.isEmpty() ?: true
            val hasItemsNow = order.carts?.isNotEmpty() ?: false

            // Save order when first item is added (cart was empty, now has items)
            if (wasEmpty && hasItemsNow) {
                val syncRequest = createSyncOrderRequest(tableId, order, state = state)
                val result = syncOrderToTableUseCase(syncRequest)
                result.execute { asyncResult ->
                    when (asyncResult) {
                        is Success -> {
                            // Order saved successfully
                            copy()
                        }

                        else -> {
                            // Handle error
                            copy()
                        }
                    }
                }
            }
            // Update order when cart changes (and it's not the first item)
            else if (!wasEmpty && hasItemsNow) {
                val updateRequest = createUpdateOrderRequest(tableId, order, state = state)
                val result = updateOrderForTableUseCase(tableId, updateRequest)
                result.execute { asyncResult ->
                    when (asyncResult) {
                        is Success -> {
                            // Order updated successfully
                            copy()
                        }

                        else -> {
                            // Handle error
                            copy()
                        }
                    }
                }
            }
        } catch (e: Exception) {
            // Handle error
        }
    }

    /**
     * Create sync order request from current order
     */
    private fun createSyncOrderRequest(
        tableId: Int,
        order: Order,
        state: ProductsScreenState
    ): SyncOrderRequest {
        val orderCourses = createOrderCoursesFromCart(order, tableId, state = state)
        return SyncOrderRequest(
            tableId = tableId,
            customerId = order.customerId ?: 0,
            businessId = order.businessId ?: prefs.store?.businessId ?: 0,
            netPayable = order.netPayable ?: 0.0,
            orderCourses = orderCourses
        )
    }

    /**
     * Create update order request from current order
     */
    private fun createUpdateOrderRequest(
        tableId: Int,
        order: Order,
        state: ProductsScreenState
    ): UpdateOrderRequest {
        val orderCourses = createOrderCoursesFromCart(order, tableId, state = state)
        return UpdateOrderRequest(
            tableId = tableId,
            customerId = order.customerId ?: 0,
            businessId = order.businessId ?: prefs.store?.businessId ?: 0,
            netPayable = order.netPayable ?: 0.0,
            orderCourses = orderCourses
        )
    }

    /**
     * Create order courses from cart items with course assignments
     */
    private fun createOrderCoursesFromCart(
        order: Order,
        tableId: Int,
        state: ProductsScreenState
    ): List<OrderCourse> {
        val cartItemsWithCourses = state.cartItemsWithCourses[tableId] ?: emptyList()

        // Group cart items by course
        val courseGroups = cartItemsWithCourses.groupBy { it.courseId }

        return courseGroups.map { (courseId, items) ->
            val courseName = state.availableCourses.find { it.name == courseId }?.name ?: courseId
            val updatedCourseName = courseName.ifEmpty { "Uncategorized" }
            val cartJson = createCartJsonFromItems(items.map { it.cart })

            OrderCourse(
                coursesName = updatedCourseName,
                cartJson = cartJson
            )
        }
    }

    /**
     * Create cart JSON from cart items
     */
    private fun createCartJsonFromItems(carts: List<Cart>): String {
        return carts.encode()
    }

    fun addItemToCart(
        order: Order, stockItem: StockItem, optionDetails: OptionDetails, quantity: Int,
        tableOrders: Map<Int, Order>,
        selectedTableIndex: Int,
        selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
        state: ProductsScreenState
    ) {
        val currentTableId = getCurrentTableId(
            selectedTables = selectedTables,
            selectedTableIndex = selectedTableIndex
        )
        val targetOrder = if (currentTableId != null) {
            tableOrders[currentTableId] ?: Order()
        } else {
            order // Fallback to passed order if no table selected
        }

        val listCart = targetOrder.carts?.toMutableList() ?: mutableListOf()
        val storeItem = stockItem.toStoreItem()

        // Auto-create and select course if no courses exist
        var updatedState = state
        val availableCourses = state.getCurrentTableAvailableCourses()
        if (availableCourses.isEmpty()) {
            // Create first course and auto-select it
            updatedState = createAndSelectFirstCourse(state)
        }

        // Get the current selected course for new items (table-specific or global)
        val currentCourseId = updatedState.getCurrentTableSelectedCourseForNewItems().ifEmpty {
            updatedState.getCurrentTableAvailableCourses().firstOrNull()?.name ?: ""
        }

        val updatedStoreItem = storeItem.copy(
            optionSets = optionDetails.optionSets ?: mutableListOf(),
            quantity = quantity,
            billAmount = calculateTotal(
                stockItem = storeItem, optionDetails = optionDetails, updatedStock = quantity
            ).billAmount ?: 0.0,
            courseId = currentCourseId // Add course ID to store item
        )

        // Check if item with same ID, options, and course already exists in cart
        val existingCartItemIndex = listCart.indexOfFirst { cartItem ->
            cartItem.storeItem?.id == stockItem.id &&
                    areOptionSetsEqual(cartItem.storeItem?.optionSets, optionDetails.optionSets) &&
                    cartItem.storeItem?.courseId == currentCourseId
        }

        if (existingCartItemIndex != -1) {
            // Item already exists, increment its quantity
            val existingCartItem = listCart[existingCartItemIndex]
            val newQuantity = (existingCartItem.quantity ?: 0) + quantity

            // Recalculate totals for the updated quantity
            val updatedStoreItemForExisting = existingCartItem.storeItem?.copy(
                quantity = newQuantity,
                billAmount = calculateTotal(
                    stockItem = existingCartItem.storeItem,
                    optionDetails = optionDetails,
                    updatedStock = newQuantity
                ).billAmount ?: 0.0
            )

            val itemPrice =
                updatedStoreItemForExisting?.price?.transformDecimal()?.toDouble() ?: 0.0
            val itemTax = updatedStoreItemForExisting?.tax?.transformDecimal()?.toDouble() ?: 0.0
            val itemDiscount =
                updatedStoreItemForExisting?.discountedAmount?.transformDecimal()?.toDouble() ?: 0.0
            val itemNetPayable =
                updatedStoreItemForExisting?.billAmount?.transformDecimal()?.toDouble() ?: 0.0

            // Update the existing cart item
            listCart[existingCartItemIndex] = existingCartItem.copy(
                storeItem = updatedStoreItemForExisting,
                quantity = newQuantity,
                price = itemPrice,
                tax = itemTax,
                discount = itemDiscount,
                netPayable = itemNetPayable
            )
        } else {
            // Item doesn't exist, add new cart item
            val itemPrice = updatedStoreItem.price?.transformDecimal()?.toDouble() ?: 0.0
            val itemTax = updatedStoreItem.tax?.transformDecimal()?.toDouble() ?: 0.0
            val itemDiscount =
                updatedStoreItem.discountedAmount?.transformDecimal()?.toDouble() ?: 0.0
            val itemNetPayable = updatedStoreItem.billAmount?.transformDecimal()?.toDouble() ?: 0.0

            listCart.add(
                Cart(
                    storeItem = updatedStoreItem,
                    quantity = quantity,
                    price = itemPrice,
                    extraPrice = 0.0,
                    tax = itemTax,
                    discount = itemDiscount,
                    netPayable = itemNetPayable,
                    optionPrice = 0.0,
                    isB1G1 = false
                )
            )
        }

        // Calculate order-level totals
        val netPayable = listCart.sumByDouble { it.netPayable ?: 0.0 }
        val totalTax = listCart.sumByDouble { it.tax ?: 0.0 }

        // Check if service charge is applied and include it in total
        val serviceChargeApplied = if (currentTableId != null) {
            state.tableServiceChargeApplied[currentTableId] ?: false
        } else {
            state.serviceChargeApplied
        }
        val serviceChargeAmount = if (serviceChargeApplied) {
            netPayable * (getServiceChargePercentage() / 100.0)
        } else {
            0.0
        }
        val totalPrice = netPayable + totalTax + serviceChargeAmount

        val updatedOrder = targetOrder.copy(
            carts = listCart,
            netPayable = netPayable,
            tax = totalTax,
            totalPrice = totalPrice
        )

        if (currentTableId != null) {
            // Update table-specific cart
            val updatedTableOrders = tableOrders.toMutableMap()
            updatedTableOrders[currentTableId] = updatedOrder

            // Auto-apply service charge if this is the first item and conditions are met
            val updatedTableServiceCharge = state.tableServiceChargeApplied.toMutableMap()
            val wasCartEmpty = (targetOrder.carts?.isEmpty() ?: true)
            val shouldAutoApply = shouldAutoApplyServiceCharge()
            val wasManuallyRemoved = state.tableServiceChargeManuallyRemoved[currentTableId] ?: false
            val isServiceChargeAlreadyApplied =
                state.tableServiceChargeApplied[currentTableId] ?: false

            if (wasCartEmpty && shouldAutoApply && !wasManuallyRemoved && !isServiceChargeAlreadyApplied) {
                updatedTableServiceCharge[currentTableId] = true
                // Recalculate total with service charge
                val serviceChargeAmount = netPayable * (getServiceChargePercentage() / 100.0)
                val finalTotalWithServiceCharge = netPayable + totalTax + serviceChargeAmount
                updatedTableOrders[currentTableId] =
                    updatedOrder.copy(totalPrice = finalTotalWithServiceCharge)
            }

            // Course was created, merge the states
           updatedState =  updatedState.copy(
                tableOrders = updatedTableOrders,
                tableServiceChargeApplied = updatedTableServiceCharge,
                showCart = updatedOrder.carts?.isNotEmpty() ?: false
            )

            setState {
                // Use updatedState to preserve any course changes from createAndSelectFirstCourse
                updatedState.copy(
                    tableOrders = updatedTableOrders,
                    tableServiceChargeApplied = updatedTableServiceCharge,
                    showCart = updatedOrder.carts?.isNotEmpty() ?: false
                )
            }
        } else {
            // Fallback to global cart
            val wasCartEmpty = (targetOrder.carts?.isEmpty() ?: true)
            val shouldAutoApply = shouldAutoApplyServiceCharge()
            val isServiceChargeAlreadyApplied = serviceChargeApplied

            if (wasCartEmpty && shouldAutoApply && !isServiceChargeAlreadyApplied) {
                // Auto-apply service charge for global cart
                val serviceChargeAmount = netPayable * (getServiceChargePercentage() / 100.0)
                val finalTotalWithServiceCharge = netPayable + totalTax + serviceChargeAmount
                val updatedOrderWithServiceCharge =
                    updatedOrder.copy(totalPrice = finalTotalWithServiceCharge)

                updatedState = updatedState.copy(
                    order = updatedOrderWithServiceCharge,
                    serviceChargeApplied = true,
                    showCart = updatedOrder.carts?.isNotEmpty() ?: false
                )
                setState {
                    // Use updatedState to preserve any course changes from createAndSelectFirstCourse
                    updatedState.copy(
                        order = updatedOrderWithServiceCharge,
                        serviceChargeApplied = true,
                        showCart = updatedOrder.carts?.isNotEmpty() ?: false
                    )
                }
            } else {
                updatedState = updatedState.copy(
                    order = updatedOrder,
                    showCart = updatedOrder.carts?.isNotEmpty() ?: false
                )
                setState {
                    // Use updatedState to preserve any course changes from createAndSelectFirstCourse
                    updatedState.copy(
                        order = updatedOrder,
                        showCart = updatedOrder.carts?.isNotEmpty() ?: false
                    )
                }
            }
        }

    // Sync course assignments after adding item
    updatedState = syncCartItemsWithCourses(state = updatedState)

    // Save/update table order if needed
    if (currentTableId != null)
    {
        viewModelScope.launch {
            saveTableOrderIfNeeded(currentTableId, updatedOrder, state = updatedState)
        }
    }
}

/**
 * Create and auto-select the first course when no courses exist
 */
private fun createAndSelectFirstCourse(state: ProductsScreenState): ProductsScreenState {
    val currentTableId = getCurrentTableId(
        selectedTables = state.selectedTables,
        selectedTableIndex = state.selectedTableIndex
    )

    return if (currentTableId != null) {
        // Handle table-specific courses
        val courseName = "Course 1"
        val newCourse = MealCourse(name = courseName)
        val updatedCourses = listOf(newCourse)

        // Update table-specific courses
        val updatedTableCourses = state.tableAvailableCourses.toMutableMap()
        updatedTableCourses[currentTableId] = updatedCourses

        // Auto-select the new course for new items (use course name as ID for consistency)
        val updatedTableSelectedCourses = state.tableSelectedCourseForNewItems.toMutableMap()
        updatedTableSelectedCourses[currentTableId] = courseName

        // Set as active course
        val updatedTableActiveCourses = state.tableActiveCourses.toMutableMap()
        updatedTableActiveCourses[currentTableId] = courseName

        state.copy(
            tableAvailableCourses = updatedTableCourses,
            tableSelectedCourseForNewItems = updatedTableSelectedCourses,
            tableActiveCourses = updatedTableActiveCourses
        )
    } else {
        // Handle global courses
        val courseName = "Course 1"
        val newCourse = MealCourse(name = courseName)
        val updatedCourses = listOf(newCourse)

        state.copy(
            availableCourses = updatedCourses,
            selectedCourseForNewItems = courseName,
            currentActiveCourse = courseName
        )
    }
}

/**
 * Helper function to compare two option sets for equality
 * Used to determine if an item with the same options already exists in cart
 */
private fun areOptionSetsEqual(
    optionSets1: List<OptionSet>?,
    optionSets2: List<OptionSet>?
): Boolean {
    if (optionSets1 == null && optionSets2 == null) return true
    if (optionSets1 == null || optionSets2 == null) return false
    if (optionSets1.size != optionSets2.size) return false

    // Sort both lists by ID for consistent comparison
    val sorted1 = optionSets1.sortedBy { it.id }
    val sorted2 = optionSets2.sortedBy { it.id }

    return sorted1.zip(sorted2).all { (set1, set2) ->
        set1.id == set2.id && areOptionsEqual(set1.options, set2.options)
    }
}

/**
 * Helper function to compare two option lists for equality
 */
private fun areOptionsEqual(
    options1: List<Option?>?,
    options2: List<Option?>?
): Boolean {
    if (options1 == null && options2 == null) return true
    if (options1 == null || options2 == null) return false
    if (options1.size != options2.size) return false

    // Sort both lists by ID for consistent comparison
    val sorted1 = options1.filterNotNull().sortedBy { it.id }
    val sorted2 = options2.filterNotNull().sortedBy { it.id }

    return sorted1.zip(sorted2).all { (option1, option2) ->
        option1.id == option2.id &&
                option1.optionchecked == option2.optionchecked &&
                option1.quantity == option2.quantity
    }
}

/**
 * Extension function to convert StoreItem to StockItem
 * Used for calculations when updating existing cart items
 */
private fun StoreItem.toStockItem(): StockItem {
    return StockItem(
        additionalInfo = this.additionalInfo,
        billAmount = this.billAmount,
        brandId = this.brandId,
        businessId = this.businessId,
        categoryId = this.categoryId,
        createdBy = this.createdBy,
        createdOn = this.createdOn,
        dailyCapacity = this.dailyCapacity,
        description = this.description,
        discountType = this.discountType,
        discountedAmount = this.discountedAmount?.toString(),
        id = this.id,
        ingredients = this.ingredients,
        modifiedBy = this.modifiedBy,
        modifiedOn = this.modifiedOn,
        name = this.name,
        pic = this.pic,
        preparationTime = this.preparationTime,
        price = this.price,
        servingSize = this.servingSize,
        stock = this.quantity,
        storeId = this.storeId,
        tax = this.tax,
        unitId = this.unitId,
        vat = this.vat
    )
}

fun removeItemFromCart(
    order: Order,
    cartItem: Cart,
    tableOrders: Map<Int, Order>,
    selectedTableIndex: Int,
    selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
    currentState: ProductsScreenState
): StateFlow<Async<Order>> {
    val flow = MutableStateFlow<Async<Order>>(Loading())
    val currentTableId = getCurrentTableId(
        selectedTableIndex = selectedTableIndex,
        selectedTables = selectedTables
    )
    val targetOrder = if (currentTableId != null) {
        tableOrders[currentTableId] ?: Order()
    } else {
        order // Fallback to passed order if no table selected
    }

    val listCart = targetOrder.carts?.toMutableList() ?: mutableListOf()

    // Remove the specific cart item using UUID for unique identification
    listCart.removeAll { it.uuid == cartItem.uuid }

    // Recalculate totals after removal
    val netPayable = listCart.sumByDouble { it.netPayable ?: 0.0 }
    val totalTax = listCart.sumByDouble { it.tax ?: 0.0 }

    // Get current state to check service charge
    var serviceChargeApplied = false
    var serviceChargeAmount = 0.0

    serviceChargeApplied = if (currentTableId != null) {
        currentState.tableServiceChargeApplied[currentTableId] ?: false
    } else {
        currentState.serviceChargeApplied
    }
    serviceChargeAmount = if (serviceChargeApplied) {
        netPayable * (getServiceChargePercentage() / 100.0)
    } else {
        0.0
    }
    val totalPrice = netPayable + totalTax + serviceChargeAmount

    val updatedOrder = targetOrder.copy(
        carts = listCart,
        netPayable = netPayable,
        tax = totalTax,
        totalPrice = totalPrice
    )

    var updatedState: ProductsScreenState = currentState

    setState {
        if (currentTableId != null) {
            // Update table-specific cart
            val updatedTableOrders = tableOrders.toMutableMap()
            updatedTableOrders[currentTableId] = updatedOrder
            updatedState = currentState.copy(
                tableOrders = updatedTableOrders,
                showCart = selectedTables.isNotEmpty()
            )
            copy(
                tableOrders = updatedTableOrders,
                showCart = selectedTables.isNotEmpty() // Show cart if tables are selected, even if empty
            )
        } else {

            updatedState = currentState.copy(
                order = updatedOrder,
                showCart = updatedOrder.carts?.isNotEmpty() ?: false
            )
            // Fallback to global cart
            copy(
                order = updatedOrder,
                showCart = updatedOrder.carts?.isNotEmpty() ?: false
            )
        }
    }

    // Sync course assignments after removing item
    syncCartItemsWithCourses(state = updatedState)

    // Check if cart is now empty and trigger complete reset if needed
    checkAndResetIfCartEmpty(updatedOrder, currentTableId)

    flow.value = Success(updatedOrder)
    return flow
}

/**
 * Simplified removeItemFromCart method that uses current state
 */
fun removeItemFromCart(cartItem: Cart, state: ProductsScreenState): StateFlow<Async<Order>> {
    val currentState = state
    return removeItemFromCart(
        order = currentState.order,
        cartItem = cartItem,
        tableOrders = currentState.tableOrders,
        selectedTableIndex = currentState.selectedTableIndex,
        selectedTables = currentState.selectedTables,
        currentState = state
    )
}

fun updateProductDetailsBottomSheetVisibility(stockItem: StockItem?) {
    setState {
        copy(isBottomSheetVisible = stockItem)
    }
}

suspend fun placeOrder(
    order: Order,
    transId: String,
    state: ProductsScreenState
): StateFlow<Async<OrderResponse2>> {
    val flow = MutableStateFlow<Async<OrderResponse2>>(Loading())
    setState {
        copy(orderResponse = Loading())
    }

    // Get current customer and add to order
    val currentCustomer = state.getCurrentCustomer()
    val orderWithCustomer = if (currentCustomer != null) {
        order.copy(
            customerId = currentCustomer.id
        )
    } else {
        order
    }

    orderUseCase(orderWithCustomer, transId = transId).execute {
        when (it) {
            is Success -> {
                flow.value = it()
                viewModelScope.launch {
                    orderSyncManager.triggerImmediateSync()
                }
                copy(orderResponse = it())
            }

            else -> {
                flow.value = Uninitialized
                copy(orderResponse = Uninitialized)
            }
        }
    }
    return flow
}

suspend fun getOptionDetails(
    itemId: Int, stockItem: StockItem
): StateFlow<Async<OptionDetails>> {
    val flow = MutableStateFlow<Async<OptionDetails>>(Loading())
    setState {
        copy(optionDetailsResponse = Loading())
    }
    getOptionDetailsUseCase(itemId = itemId).execute {
        when (it) {
            is Success -> {
                flow.value = it()
                copy(
                    optionDetailsResponse = it(), productTotal = calculateTotal(
                        stockItem = stockItem.toStoreItem(),
                        optionDetails = it()() ?: OptionDetails(),
                        updatedStock = 1
                    ).billAmount ?: 0.0
                )
            }

            else -> {
                flow.value = Uninitialized
                copy(optionDetailsResponse = Uninitialized)
            }
        }
    }
    return flow
}


fun updateSelectedOptionCondition1(
    option: Option,
    stock: Int,
    stockItem: StockItem,
    optionDetails: OptionDetails,
    currentOptionSet: OptionSet
) {

    val options = optionDetails.optionSets?.flatMap { optionSet ->
        if (optionSet.id == currentOptionSet.id) {
            optionSet.options.map { currentOption ->
                if (currentOption?.id == option.id) {
                    currentOption?.copy(
                        optionchecked = true, quantity = 1
                    )
                } else {
                    currentOption?.copy(optionchecked = false, quantity = 0)
                }
            }
        } else mutableListOf()
    }
    val optionSets = optionDetails.optionSets?.map {
        if (it.id == currentOptionSet.id) {
            it.copy(options = options ?: mutableListOf())
        } else {
            it
        }
    }
    val updatedOptionDetails = optionDetails.copy(optionSets = optionSets)
    setState {
        copy(
            optionDetailsResponse = Success(updatedOptionDetails),
            productTotal = calculateTotal(
                stockItem.toStoreItem(), updatedOptionDetails, updatedStock = stock
            ).billAmount ?: 0.0
        )
    }
}

fun addSelectedOptionCondition2(
    optionDetails: OptionDetails,
    currentOptionSet: OptionSet,
    option: Option,
    stockItem: StoreItem,
    stock: Int
) {
    val options = optionDetails.optionSets?.flatMap { optionSet ->
        if (optionSet.id == currentOptionSet.id) {
            optionSet.options.map { currentOption ->
                if (currentOption?.id == option.id) {
                    currentOption?.copy(
                        optionchecked = true, quantity = 1
                    )
                } else {
                    currentOption
                }
            }
        } else mutableListOf()
    }
    val optionSets = optionDetails.optionSets?.map {
        if (it.id == currentOptionSet.id) {
            it.copy(options = options ?: mutableListOf())
        } else {
            it
        }
    }
    val updatedOptionDetails = optionDetails.copy(optionSets = optionSets)
    setState {
        copy(
            optionDetailsResponse = Success(updatedOptionDetails),
            productTotal = calculateTotal(
                stockItem, updatedOptionDetails, updatedStock = stock
            ).billAmount ?: 0.0
        )
    }
}

fun removeSelectedOptionCondition2(
    optionDetails: OptionDetails,
    option: Option,
    currentOptionSet: OptionSet,
    stockItem: StoreItem,
    stock: Int
) {
    val options = optionDetails.optionSets?.flatMap { optionSet ->
        if (optionSet.id == currentOptionSet.id) {
            optionSet.options.map { currentOption ->
                if (currentOption?.id == option.id) {
                    currentOption?.copy(optionchecked = false, quantity = 0)
                } else {
                    currentOption
                }
            }
        } else {
            mutableListOf()
        }
    }
    val optionSets = optionDetails.optionSets?.map {
        if (it.id == currentOptionSet.id) {
            it.copy(options = options ?: mutableListOf())
        } else {
            it
        }
    }

    val updatedOptionDetails = optionDetails.copy(optionSets = optionSets)

    setState {
        copy(
            optionDetailsResponse = Success(updatedOptionDetails),
            productTotal = calculateTotal(
                stockItem, updatedOptionDetails, updatedStock = stock
            ).billAmount ?: 0.0,
        )
    }
}

fun calculateTotal(
    stockItem: StoreItem, optionDetails: OptionDetails, updatedStock: Int
): StoreItem {
    if (optionDetails.optionSets?.isNotEmpty() == true) {
        val optionPrice = optionDetails.optionSets.flatMap { optionSet ->
            optionSet.options.map { option ->
                if (option?.optionchecked == true) {
                    (option.price ?: 0.0) * (option.quantity?.toDouble() ?: 0.0)
                } else {
                    0.0
                }
            }
        }.sum()
        val price = stockItem.price ?: 0.0
        val tax = stockItem.tax ?: 0.0
        val discount = stockItem.discountedAmount ?: 0.0
        val total = (price + tax - discount + optionPrice) * (updatedStock)
        return stockItem.copy(
            price = price, tax = tax, discountedAmount = discount, billAmount = total
        )
    } else {
        val price = stockItem.price ?: 0.0
        val tax = stockItem.tax ?: 0.0
        val discount = stockItem.discountedAmount ?: 0.0
        val total = (price + tax - discount) * (updatedStock)
        return stockItem.copy(
            price = price, tax = tax, discountedAmount = discount, billAmount = total
        )
    }
}

fun updateOptionStock(
    option: Option,
    currentOptionSet: OptionSet,
    stock: Int,
    optionStock: Int,
    optionDetails: OptionDetails,
    stockItem: StoreItem
) {
    val options = optionDetails.optionSets?.flatMap { optionSet ->
        if (optionSet.id == currentOptionSet.id) {
            optionSet.options.map { currentOption ->
                if (currentOption?.id == option.id) {
                    if (optionStock > 0) {
                        currentOption?.copy(optionchecked = true, quantity = optionStock)
                    } else {
                        currentOption?.copy(optionchecked = false, quantity = optionStock)
                    }
                } else {
                    currentOption
                }
            }
        } else {
            mutableListOf()
        }
    }
    val optionSets = optionDetails.optionSets?.map {
        if (it.id == currentOptionSet.id) {
            it.copy(options = options ?: mutableListOf())
        } else {
            it
        }
    }


    val updatedOptionDetails = optionDetails.copy(optionSets = optionSets)

    setState {
        copy(
            optionDetailsResponse = Success(updatedOptionDetails),
            stock = stock,
            productTotal = calculateTotal(
                stockItem, updatedOptionDetails, updatedStock = stock
            ).billAmount ?: 0.0
        )
    }
}

fun updateShowPrintingPreview(order: OrderItem2?, shouldPrintInstant: Boolean = false) {
    setState {
        copy(isShowPrintingPreview = order, shouldPrintInstant = shouldPrintInstant)
    }
}

fun showSalesReportDialog(show: Boolean) {
    setState {
        copy(showSalesReportDialog = show)
    }
}

fun updateOrder(order: Order) {
    setState {
        val currentTableId = getCurrentTableId()
        if (currentTableId != null) {
            // Update table-specific cart
            val updatedTableOrders = tableOrders.toMutableMap()
            updatedTableOrders[currentTableId] = order
            copy(tableOrders = updatedTableOrders)
        } else {
            // Fallback to global cart
            copy(order = order)
        }
    }
}

suspend fun updateCartVisibility(visible: Boolean) {
    setState {
        if (!visible) {
            // When closing cart, unselect current table
            copy(
                showCart = false,
                selectedTableIndex = -1 // Unselect current table
            )
        } else {
            copy(showCart = true)
        }
    }
}

/**
 * Link a customer from rewards to the current table or global order
 */
fun linkCustomerToCurrentTable(customer: com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer?) {
    setState {
        val currentTableId = getCurrentTableId()
        if (currentTableId != null) {
            // Link customer to specific table
            val updatedTableCustomers = tableCustomers.toMutableMap()
            if (customer != null) {
                updatedTableCustomers[currentTableId] = customer
            } else {
                updatedTableCustomers.remove(currentTableId)
            }
            copy(tableCustomers = updatedTableCustomers)
        } else {
            // Link customer to global order (walk-in)
            copy(selectedCustomer = customer)
        }
    }
}

/**
 * Link a customer to a specific table
 */
fun linkCustomerToTable(
    tableId: Int,
    customer: com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer?
) {
    setState {
        val updatedTableCustomers = tableCustomers.toMutableMap()
        if (customer != null) {
            updatedTableCustomers[tableId] = customer
        } else {
            updatedTableCustomers.remove(tableId)
        }
        copy(tableCustomers = updatedTableCustomers)
    }
}

/**
 * Clear customer from current table or global order
 */
fun clearCurrentCustomer() {
    linkCustomerToCurrentTable(null)
}

fun clearGlobalCustomer() {
    setState {
        copy(selectedCustomer = null)
    }
}

/**
 * Clear customer from specific table
 */
fun clearTableCustomer(tableId: Int) {
    linkCustomerToTable(tableId, null)
}

/**
 * Convert RewardsCustomer to Customer for order creation
 */
private fun convertRewardsCustomerToCustomer(rewardsCustomer: com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer): com.thedasagroup.suminative.data.model.response.store_orders.Customer {
    return com.thedasagroup.suminative.data.model.response.store_orders.Customer(
        id = rewardsCustomer.id,
        businessId = rewardsCustomer.businessId,
        name = rewardsCustomer.name,
        email = rewardsCustomer.email,
        phone = rewardsCustomer.phone,
        pic = rewardsCustomer.pic,
        isEmailVerified = if (rewardsCustomer.isEmailVerified == true) 1 else 0,
        isMobileVerified = if (rewardsCustomer.isMobileVerified == true) 1 else 0,
        isSocialUser = rewardsCustomer.isSocialUser,
        type = rewardsCustomer.type
    )
}

/**
 * Link a rewards customer to the current table/order
 * This method should be called when a customer is selected from the rewards system
 */
fun linkRewardsCustomer(rewardsCustomer: com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer?) {
    linkCustomerToCurrentTable(rewardsCustomer)
}

/**
 * Check if cart is empty after item removal and trigger complete reset if needed
 */
private fun checkAndResetIfCartEmpty(updatedOrder: Order, currentTableId: Int?) {
    val isEmpty = updatedOrder.carts?.isEmpty() ?: true

    if (isEmpty) {
        setState {
            if (currentTableId != null) {
                // Clear everything for table mode and remove all tables
                copy(
                    selectedTables = emptyList(),
                    selectedTableIndex = 0,
                    tableOrders = emptyMap(),
                    tableCustomers = emptyMap(), // Clear table customers
                    cartItemsWithCourses = emptyMap(),
                    tableCourseStatuses = emptyMap(),
                    tableActiveCourses = emptyMap(),
                    selectedCourseFilter = emptyMap(),
                    availableCourses = emptyList(),
                    selectedCourseForNewItems = "",
                    tableServiceChargeApplied = emptyMap(),
                    tableServiceChargeManuallyRemoved = emptyMap(),
                    showCart = false
                )
            } else {
                // Clear everything for walk-in mode
                copy(
                    order = Order(
                        carts = emptyList(),
                        netPayable = 0.0,
                        tax = 0.0,
                        totalPrice = 0.0
                    ),
                    selectedCustomer = null, // Clear global customer
                    globalCartItemsWithCourses = emptyList(),
                    courseStatuses = emptyMap(),
                    currentActiveCourse = null,
                    availableCourses = emptyList(),
                    selectedCourseForNewItems = "",
                    showCart = false
                )
            }
        }
    }
}

fun clearCart() {
    setState {
        val currentTableId = getCurrentTableId()
        if (currentTableId != null) {
            // Clear the current table's cart and reset course statuses
            val updatedTableOrders = tableOrders.toMutableMap()
            updatedTableOrders[currentTableId] = Order(
                carts = emptyList(),
                netPayable = 0.0,
                tax = 0.0,
                totalPrice = 0.0
            )

            // Clear table-specific cart items with courses
            val updatedCartItemsWithCourses = cartItemsWithCourses.toMutableMap()
            updatedCartItemsWithCourses[currentTableId] = emptyList()

            // Reset course statuses for this table
            val updatedTableCourseStatuses = tableCourseStatuses.toMutableMap()
            updatedTableCourseStatuses.remove(currentTableId)

            // Reset active course for this table to first course
            val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
            updatedTableActiveCourses[currentTableId] = availableCourses.firstOrNull()?.name ?: ""

            // Reset service charge tracking for this table
            val updatedTableServiceChargeApplied = tableServiceChargeApplied.toMutableMap()
            updatedTableServiceChargeApplied.remove(currentTableId)
            val updatedTableServiceChargeManuallyRemoved =
                tableServiceChargeManuallyRemoved.toMutableMap()
            updatedTableServiceChargeManuallyRemoved.remove(currentTableId)

            copy(
                tableOrders = updatedTableOrders,
                cartItemsWithCourses = updatedCartItemsWithCourses,
                tableCourseStatuses = updatedTableCourseStatuses,
                tableActiveCourses = updatedTableActiveCourses,
                tableServiceChargeApplied = updatedTableServiceChargeApplied,
                tableServiceChargeManuallyRemoved = updatedTableServiceChargeManuallyRemoved
            )
        } else {
            // Clear global cart and reset global course statuses
            copy(
                order = Order(
                    carts = emptyList(),
                    netPayable = 0.0,
                    tax = 0.0,
                    totalPrice = 0.0
                ),
                globalCartItemsWithCourses = emptyList(),
                courseStatuses = emptyMap(),
                currentActiveCourse = availableCourses.firstOrNull()?.name
            )
        }
    }
}

/**
 * Handle payment completion by calling Delete Table Order API and toggleOccupied API
 * This is called when payment is successfully completed for a table
 */
suspend fun handlePaymentCompletion(tableId: Int) {
    try {
        // First, delete the table order
        deleteOrderForTableUseCase(tableId).execute { asyncResult ->
            when (asyncResult) {
                is Success -> {
                    copy()
                }

                else -> {
                    copy()
                }
            }
        }
    } catch (e: Exception) {
        Log.e("ProductScreenViewModel", e.message.toString())
        // Handle error - still try to toggle occupied status
//            toggleTableOccupiedAfterPayment(tableId)
    }
}

/**
 * Toggle table occupied status to false with netPayable 0 after payment completion
 */
private suspend fun toggleTableOccupiedAfterPayment(tableId: Int) {
    try {
        val toggleResult = toggleTableOccupiedUseCase(tableId, 0.0)
        toggleResult.execute { asyncResult ->
            when (asyncResult) {
                is Success -> {
                    // Successfully toggled occupied status to false
                    copy()
                }

                else -> {
                    // Handle error
                    copy()
                }
            }
        }
    } catch (e: Exception) {
        // Handle error
    }
}

/**
 * Clear cart and remove table after successful order completion
 * This function handles the complete cleanup after payment success
 * Calls Delete Table Order API and toggleOccupied API
 */
fun clearCartAndRemoveTable(state: ProductsScreenState) {
    val currentTableId = state.getCurrentTableId()
    if (currentTableId != null) {
        // Call Delete Table Order API and toggle occupied status
        viewModelScope.launch(Dispatchers.IO) {
            handlePaymentCompletion(currentTableId)
        }
        // Remove the current table from selected tables
        val updatedTables = state.selectedTables.filter { it.tableId != currentTableId }

        // Remove all table-specific data for this table
        val updatedTableOrders = state.tableOrders.toMutableMap()
        updatedTableOrders.remove(currentTableId)

        val updatedCartItemsWithCourses = state.cartItemsWithCourses.toMutableMap()
        updatedCartItemsWithCourses.remove(currentTableId)

        val updatedTableCourseStatuses = state.tableCourseStatuses.toMutableMap()
        updatedTableCourseStatuses.remove(currentTableId)

        val updatedTableActiveCourses = state.tableActiveCourses.toMutableMap()
        updatedTableActiveCourses.remove(currentTableId)

        val updatedSelectedCourseFilter = state.selectedCourseFilter.toMutableMap()
        updatedSelectedCourseFilter.remove(currentTableId)

        val updatedTableServiceChargeApplied = state.tableServiceChargeApplied.toMutableMap()
        updatedTableServiceChargeApplied.remove(currentTableId)

        val updatedTableServiceChargeManuallyRemoved =
            state.tableServiceChargeManuallyRemoved.toMutableMap()
        updatedTableServiceChargeManuallyRemoved.remove(currentTableId)

        // Update selected table index
        val newSelectedIndex = when {
            updatedTables.isEmpty() -> 0
            state.selectedTableIndex >= updatedTables.size -> updatedTables.size - 1
            else -> state.selectedTableIndex
        }
        val tableAvailableCourses = state.tableAvailableCourses.toMutableMap()
        tableAvailableCourses[currentTableId] = mutableListOf()
        setState {
            copy(
                tableAvailableCourses = tableAvailableCourses,
                selectedTables = updatedTables,
                selectedTableIndex = newSelectedIndex,
                tableOrders = updatedTableOrders,
                cartItemsWithCourses = updatedCartItemsWithCourses,
                tableCourseStatuses = updatedTableCourseStatuses,
                tableActiveCourses = updatedTableActiveCourses,
                selectedCourseFilter = updatedSelectedCourseFilter,
                tableServiceChargeApplied = updatedTableServiceChargeApplied,
                tableServiceChargeManuallyRemoved = updatedTableServiceChargeManuallyRemoved,
                showCart = updatedTables.isNotEmpty() // Show cart if there are still tables, hide if no tables
            )
        }

    } else {
        // Clear global cart and reset global course statuses
        setState {
            copy(
                order = Order(
                    carts = emptyList(),
                    netPayable = 0.0,
                    tax = 0.0,
                    totalPrice = 0.0
                ),
                availableCourses = mutableListOf(),
                globalCartItemsWithCourses = emptyList(),
                courseStatuses = emptyMap(),
                currentActiveCourse = availableCourses.firstOrNull()?.name,
                showCart = false
            )
        }
    }
}

/**
 * Update the order for a specific table
 */
private fun updateTableOrder(tableId: Int, updatedOrder: Order) {
    setState {
        val updatedTableOrders = tableOrders.toMutableMap()
        updatedTableOrders[tableId] = updatedOrder
        copy(tableOrders = updatedTableOrders)
    }
}

/**
 * Get the current table's order
 */
//    private fun getCurrentTableOrder(): Order {
//        val currentTableId = getCurrentTableId()
//        return if (currentTableId != null) {
//            tableOrders[currentTableId] ?: Order()
//        } else {
//            order // Fallback to global order
//        }
//    }

/**
 * Update course assignment for a cart item
 */
fun updateCartItemCourse(cartUuid: String, newCourseId: String) {
    setState {
        val currentTableId = getCurrentTableId()
        if (currentTableId != null) {
            // Table-specific course assignment
            val currentCartItems = cartItemsWithCourses[currentTableId] ?: emptyList()
            val updatedCartItems = currentCartItems.map { item ->
                if (item.cart.uuid == cartUuid) {
                    // Update both the course assignment and the store item's course ID
                    val updatedStoreItem = item.cart.storeItem?.copy(courseId = newCourseId)
                    val updatedCart = item.cart.copy(storeItem = updatedStoreItem)
                    item.copy(cart = updatedCart, courseId = newCourseId)
                } else {
                    item
                }
            }

            // Also update the actual table order
            val currentOrder = tableOrders[currentTableId] ?: Order()
            val updatedCarts = currentOrder.carts?.map { cart ->
                if (cart.uuid == cartUuid) {
                    val updatedStoreItem = cart.storeItem?.copy(courseId = newCourseId)
                    cart.copy(storeItem = updatedStoreItem)
                } else {
                    cart
                }
            } ?: emptyList()
            val updatedOrder = currentOrder.copy(carts = updatedCarts)
            val updatedTableOrders = tableOrders.toMutableMap()
            updatedTableOrders[currentTableId] = updatedOrder

            val updatedCartItemsWithCourses = cartItemsWithCourses.toMutableMap()
            updatedCartItemsWithCourses[currentTableId] = updatedCartItems
            copy(
                cartItemsWithCourses = updatedCartItemsWithCourses,
                tableOrders = updatedTableOrders
            )
        } else {
            // Global course assignment when no tables
            val updatedGlobalCartItems = globalCartItemsWithCourses.map { item ->
                if (item.cart.uuid == cartUuid) {
                    // Update both the course assignment and the store item's course ID
                    val updatedStoreItem = item.cart.storeItem?.copy(courseId = newCourseId)
                    val updatedCart = item.cart.copy(storeItem = updatedStoreItem)
                    item.copy(cart = updatedCart, courseId = newCourseId)
                } else {
                    item
                }
            }

            // Also update the global order
            val updatedCarts = order.carts?.map { cart ->
                if (cart.uuid == cartUuid) {
                    val updatedStoreItem = cart.storeItem?.copy(courseId = newCourseId)
                    cart.copy(storeItem = updatedStoreItem)
                } else {
                    cart
                }
            } ?: emptyList()
            val updatedOrder = order.copy(carts = updatedCarts)

            copy(
                globalCartItemsWithCourses = updatedGlobalCartItems,
                order = updatedOrder
            )
        }
    }
}

/**
 * Update course filter for current table
 */
fun updateCourseFilter(filter: CourseFilter) {
    setState {
        val currentTableId = getCurrentTableId()
        if (currentTableId != null) {
            // Table-specific filter
            val updatedFilters = selectedCourseFilter.toMutableMap()
            updatedFilters[currentTableId] = filter
            copy(selectedCourseFilter = updatedFilters)
        } else {
            // Global filter when no tables
            copy(globalSelectedCourseFilter = filter)
        }
    }
}

/**
 * Sync cart items with course assignments when order changes
 */
fun syncCartItemsWithCourses(state: ProductsScreenState): ProductsScreenState {
    var updatedState: ProductsScreenState
    val currentTableId = state.getCurrentTableId()
    if (currentTableId != null) {
        // Table-specific sync
        val currentOrder = state.tableOrders[currentTableId] ?: Order()
        // Use cart UUID as key to ensure uniqueness across courses
        val existingAssignments =
            state.cartItemsWithCourses[currentTableId]?.associateBy { it.cart.uuid }
                ?: emptyMap()

        val updatedCartItems = currentOrder.carts?.map { cart ->
            val course = existingAssignments[cart.uuid] ?: CartItemWithCourse(
                cart = cart,
                courseId = cart.storeItem?.courseId
                    ?: if (state.getCurrentTableSelectedCourseForNewItems()
                            .isNotEmpty()
                    ) state.getCurrentTableSelectedCourseForNewItems() else state.getCurrentTableAvailableCourses()
                        .firstOrNull()?.name
                        ?: ""
            )
            course.copy(cart = cart)
        } ?: emptyList()

        val updatedCartItemsWithCourses = state.cartItemsWithCourses.toMutableMap()
        updatedCartItemsWithCourses[currentTableId] = updatedCartItems
        updatedState = state.copy(
            cartItemsWithCourses = updatedCartItemsWithCourses
        )
        setState {
            copy(cartItemsWithCourses = updatedCartItemsWithCourses)
        }
    } else {
        // Global sync when no tables
        val currentOrder = state.order
        // Use cart UUID as key to ensure uniqueness across courses
        val existingAssignments =
            state.globalCartItemsWithCourses.associateBy { it.cart.uuid }

        val updatedGlobalCartItems = currentOrder.carts?.map { cart ->
            val course = existingAssignments[cart.uuid] ?: CartItemWithCourse(
                cart = cart,
                courseId = cart.storeItem?.courseId
                    ?: if (state.getCurrentTableSelectedCourseForNewItems()
                            .isNotEmpty()
                    ) state.getCurrentTableSelectedCourseForNewItems() else state.getCurrentTableAvailableCourses()
                        .firstOrNull()?.name
                        ?: ""
            )
            course.copy(cart = cart)
        } ?: emptyList()
        updatedState = state.copy(
            globalCartItemsWithCourses = updatedGlobalCartItems
        )
        setState {
            copy(globalCartItemsWithCourses = updatedGlobalCartItems)
        }
    }
    return updatedState
}


/**
 * Initialize course assignments and courses for a new table
 */
fun initializeTableCourses(tableId: Int) {
    setState {
        val updatedCartItemsWithCourses = cartItemsWithCourses.toMutableMap()
        val updatedFilters = selectedCourseFilter.toMutableMap()
        val updatedTableCourses = tableAvailableCourses.toMutableMap()
        val updatedTableSelectedCourses = tableSelectedCourseForNewItems.toMutableMap()
        val updatedTableActiveCourses = tableActiveCourses.toMutableMap()

        if (!updatedCartItemsWithCourses.containsKey(tableId)) {
            updatedCartItemsWithCourses[tableId] = emptyList()
        }
        if (!updatedFilters.containsKey(tableId)) {
            updatedFilters[tableId] = CourseFilter.ALL
        }

        // Initialize table-specific courses if not already present
        if (!updatedTableCourses.containsKey(tableId)) {
            // Start with default courses for new table
            val defaultCourses = listOf(
                MealCourse("Course 1")
            )
            updatedTableCourses[tableId] = defaultCourses
            updatedTableSelectedCourses[tableId] = defaultCourses.firstOrNull()?.name ?: ""
            updatedTableActiveCourses[tableId] = defaultCourses.firstOrNull()?.name ?: ""
        }

        copy(
            cartItemsWithCourses = updatedCartItemsWithCourses,
            selectedCourseFilter = updatedFilters,
            tableAvailableCourses = updatedTableCourses,
            tableSelectedCourseForNewItems = updatedTableSelectedCourses,
            tableActiveCourses = updatedTableActiveCourses
        )
    }

    // Initialize service charge for the table if auto-apply is enabled
    initializeServiceChargeForTable(tableId)
}

/**
 * Get the current table ID
 */
private fun getCurrentTableId(
    selectedTableIndex: Int,
    selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>
): Int? {
    return if (selectedTables.isNotEmpty() && selectedTableIndex < selectedTables.size && selectedTableIndex >= 0) {
        selectedTables[selectedTableIndex].tableId
    } else {
        null
    }
}

/**
 * Handle table selection based on occupied status
 * a) occupied==true: Call Get Table Order API and load details
 * b) occupied==false: Call toggleOccupied API and show ordering page
 */
fun handleTableSelectionWithOccupiedStatus(
    selection: AreaTableSelectionHelper.AreaTableSelection,
    table: com.thedasagroup.suminative.data.model.response.reservations.Table,
    state: ProductsScreenState
) {
    viewModelScope.launch {
        if (table.occupied) {
            // Table is occupied - get existing order details
            handleOccupiedTableSelection(selection, table, state = state)
        } else {
            // Table is not occupied - toggle occupied status and show ordering page
            handleUnoccupiedTableSelection(selection, table, state = state)
        }
    }
}

/**
 * Handle selection of an occupied table
 * Calls Get Table Order API and loads the existing order details
 */
private suspend fun handleOccupiedTableSelection(
    selection: AreaTableSelectionHelper.AreaTableSelection,
    table: com.thedasagroup.suminative.data.model.response.reservations.Table,
    state: ProductsScreenState
) {
    try {
        getSyncedOrderForTableUseCase(table.id).execute { asyncResult ->
            when (asyncResult) {
                is Success -> {
                    val response = asyncResult()()
                    if (response?.success == true && response.data != null) {
                        // Load the existing order data into the table
                        val updatedState =
                            loadExistingOrderToTable(
                                selection.tableId,
                                response.data,
                                state = state
                            )
                        // Add table and show cart
                        addSelectedTable(selection, state = updatedState)
                        copy()
                    } else {
                        // Handle error - still add table but show empty cart
                        addSelectedTable(selection, state = state)
                        copy()
                    }
                }

                else -> {
                    // Handle error - still add table but show empty cart
                    addSelectedTable(selection, state = state)
                    copy()
                }
            }
        }
    } catch (e: Exception) {
        // Handle error - still add table but show empty cart
        addSelectedTable(selection, state = state)
    }
}

suspend fun syncTable(state: ProductsScreenState) {
    getSyncedOrderForTableUseCase(state.getCurrentTableId() ?: -1).execute { asyncResult ->
        when (asyncResult) {
            is Success -> {
                val response = asyncResult()()
                if (response?.success == true && response.data != null) {
                    // Load the existing order data into the table
                    val updatedState =
                        loadExistingOrderToTable(
                            state.getCurrentTableId() ?: -1,
                            response.data,
                            state = state
                        )
                    // Add table and show cart
//                        addSelectedTable(selection, state = updatedState)
                    copy()
                } else {
                    // Handle error - still add table but show empty cart
//                        addSelectedTable(selection, state = state)
                    copy()
                }
            }

            else -> {
                // Handle error - still add table but show empty cart
//                    addSelectedTable(selection, state = state)
                copy()
            }
        }
    }
}

/**
 * Handle selection of an unoccupied table
 * Creates a new sync order for the empty table and shows ordering page
 */
suspend fun handleUnoccupiedTableSelection(
    selection: AreaTableSelectionHelper.AreaTableSelection,
    table: com.thedasagroup.suminative.data.model.response.reservations.Table,
    state: ProductsScreenState
) {
    try {
        // Create a new sync order for the empty table
        val syncRequest = createInitialSyncOrderRequest(table.id, state)
        val result = syncOrderToTableUseCase(syncRequest)
        result.execute { asyncResult ->
            when (asyncResult) {
                is Success -> {
                    // Successfully created new sync order
                    addSelectedTable(selection, state = state)
                    copy()
                }

                else -> {
                    // Handle error - still add table
                    addSelectedTable(selection, state = state)
                    copy()
                }
            }
        }
    } catch (e: Exception) {
        // Handle error - still add table
        addSelectedTable(selection, state = state)
    }
}

/**
 * Create initial sync order request for an empty table
 */
private fun createInitialSyncOrderRequest(
    tableId: Int,
    state: ProductsScreenState
): SyncOrderRequest {
    val currentCustomer = state.getCurrentCustomer()
    return SyncOrderRequest(
        tableId = tableId,
        customerId = currentCustomer?.id ?: 0,
        businessId = prefs.store?.businessId ?: 0,
        netPayable = 0.0,
        orderCourses = emptyList() // Start with empty order courses
    )
}

/**
 * Load existing order data into the selected table
 */
private fun loadExistingOrderToTable(
    tableId: Int,
    orderData: com.thedasagroup.suminative.data.model.response.table_sync.SyncOrderData,
    state: ProductsScreenState
): ProductsScreenState {
    // Convert sync order data to internal Order format
    var carts = mutableListOf<Cart>()
    val cartItemsWithCourses = mutableListOf<CartItemWithCourse>()

    // Create a mapping of course names to course IDs
    val courseNameToIdMap = mutableMapOf<String, String>()
    orderData.orderCourses.forEach { course ->
        val courseId = course.coursesName
        courseNameToIdMap[course.coursesName] = courseId
    }

    orderData.orderCourses.forEach { course ->
        val courseId = courseNameToIdMap[course.coursesName] ?: ""

        // Parse the cartJson to extract items
        course.getCart().forEach { cart ->
            carts.add(cart)

            // Add cart item with proper course assignment
            cartItemsWithCourses.add(
                CartItemWithCourse(
                    cart = cart,
                    courseId = courseId
                )
            )
        }
    }

    val order = Order(
        carts = carts,
        netPayable = orderData.netPayable,
        customerId = orderData.customerId,
        businessId = orderData.businessId
    )

    // Update table orders
    val updatedTableOrders = state.tableOrders.toMutableMap()
    updatedTableOrders[tableId] = order

    // Update cart items with courses
    val updatedCartItemsWithCourses = state.cartItemsWithCourses.toMutableMap()
    updatedCartItemsWithCourses[tableId] = cartItemsWithCourses

    // Load table-specific available courses from the existing order data
    val tableSpecificCourses = extractCoursesFromOrderData(orderData)
    val updatedTableAvailableCourses = state.tableAvailableCourses.toMutableMap()
    updatedTableAvailableCourses[tableId] = tableSpecificCourses

    setState {
        copy(
            tableOrders = updatedTableOrders,
            cartItemsWithCourses = updatedCartItemsWithCourses,
            tableAvailableCourses = updatedTableAvailableCourses
        )
    }

    return state.copy(
        tableOrders = updatedTableOrders,
        cartItemsWithCourses = updatedCartItemsWithCourses,
        tableAvailableCourses = updatedTableAvailableCourses
    )
}

/**
 * Extract available courses from existing order data
 * Creates MealCourse objects based on the course names in the order
 */
private fun extractCoursesFromOrderData(
    orderData: com.thedasagroup.suminative.data.model.response.table_sync.SyncOrderData
): List<MealCourse> {
    val extractedCourses = mutableListOf<MealCourse>()

    // Extract unique course names from the order data
    val courseNames = orderData.orderCourses.map { it.coursesName }.distinct()

    courseNames.forEachIndexed { index, courseName ->
        val mealCourse = MealCourse(
            name = courseName,
        )
        extractedCourses.add(mealCourse)
    }

//        // If no courses found in order data, add default courses
//        if (extractedCourses.isEmpty()) {
//            extractedCourses.addAll(
//                listOf(
//                    MealCourse("course_starters", "Starters", "Starters"),
//                    MealCourse("course_mains", "Mains", "Mains"),
//                    MealCourse("course_desserts", "Desserts", "Desserts")
//                )
//            )
//        }

    return extractedCourses
}

/**
 * Add a selected table to the list
 */
fun addSelectedTable(
    selection: AreaTableSelectionHelper.AreaTableSelection,
    state: ProductsScreenState
) {
    val updatedTables = state.selectedTables.toMutableList()
    // Check if table is already selected
    if (!updatedTables.any { it.tableId == selection.tableId }) {
        updatedTables.add(selection)
        // Initialize empty cart for new table if it doesn't exist
        val updatedTableOrders = state.tableOrders.toMutableMap()
        if (!updatedTableOrders.containsKey(selection.tableId)) {
            updatedTableOrders[selection.tableId] = Order()
        }

        // Initialize course state for new table
        val updatedCartItemsWithCourses = state.cartItemsWithCourses.toMutableMap()
        val updatedFilters = state.selectedCourseFilter.toMutableMap()
        if (!updatedCartItemsWithCourses.containsKey(selection.tableId)) {
            updatedCartItemsWithCourses[selection.tableId] = emptyList()
        }
        if (!updatedFilters.containsKey(selection.tableId)) {
            updatedFilters[selection.tableId] = CourseFilter.ALL
        }

        // Initialize table courses if not already present (but don't auto-create courses yet)
        // Auto-course creation will happen when first item is added
        val updatedTableCourses = state.tableAvailableCourses.toMutableMap()
        val updatedTableSelectedCourses = state.tableSelectedCourseForNewItems.toMutableMap()
        val updatedTableActiveCourses = state.tableActiveCourses.toMutableMap()

        if (!updatedTableCourses.containsKey(selection.tableId)) {
            updatedTableCourses[selection.tableId] = emptyList() // Start with no courses
            updatedTableSelectedCourses[selection.tableId] = ""
            updatedTableActiveCourses[selection.tableId] = ""
        }

        setState {
            copy(
                selectedTables = updatedTables,
                selectedTableIndex = updatedTables.size - 1, // Select the newly added table
                tableOrders = updatedTableOrders,
                cartItemsWithCourses = updatedCartItemsWithCourses,
                selectedCourseFilter = updatedFilters,
                tableAvailableCourses = updatedTableCourses,
                tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                tableActiveCourses = updatedTableActiveCourses,
                showCart = true // Always show cart when table is added, even if empty
            )

        }
    } else {
        // If table already exists, just select it
        val existingIndex = updatedTables.indexOfFirst { it.tableId == selection.tableId }
        setState {
            copy(
                selectedTableIndex = existingIndex,
                showCart = true // Always show cart when table is selected, even if empty
            )
        }
    }
}

/**
 * Remove a selected table from the list
 */
fun removeSelectedTable(tableId: Int) {
    // Clean up course statuses for the removed table
    onTableRemoved(tableId)

    setState {
        val updatedTables = selectedTables.filter { it.tableId != tableId }
        val updatedTableOrders = tableOrders.toMutableMap()
        // Remove the table's cart data
        updatedTableOrders.remove(tableId)

        // Remove table-specific customer
        val updatedTableCustomers = tableCustomers.toMutableMap()
        updatedTableCustomers.remove(tableId)

        // Remove table-specific cart items with courses
        val updatedCartItemsWithCourses = cartItemsWithCourses.toMutableMap()
        updatedCartItemsWithCourses.remove(tableId)

        // Remove table-specific course filter
        val updatedSelectedCourseFilter = selectedCourseFilter.toMutableMap()
        updatedSelectedCourseFilter.remove(tableId)

        // Remove table-specific service charge tracking
        val updatedTableServiceChargeApplied = tableServiceChargeApplied.toMutableMap()
        updatedTableServiceChargeApplied.remove(tableId)
        val updatedTableServiceChargeManuallyRemoved =
            tableServiceChargeManuallyRemoved.toMutableMap()
        updatedTableServiceChargeManuallyRemoved.remove(tableId)

        val newSelectedIndex = when {
            updatedTables.isEmpty() -> 0
            selectedTableIndex >= updatedTables.size -> updatedTables.size - 1
            else -> selectedTableIndex
        }

        // Update cart visibility based on new selected table
        val newTableOrder =
            if (updatedTables.isNotEmpty() && newSelectedIndex < updatedTables.size) {
                val newTableId = updatedTables[newSelectedIndex].tableId
                updatedTableOrders[newTableId] ?: Order()
            } else {
                Order()
            }

        copy(
            selectedTables = updatedTables,
            selectedTableIndex = newSelectedIndex,
            tableOrders = updatedTableOrders,
            tableCustomers = updatedTableCustomers,
            cartItemsWithCourses = updatedCartItemsWithCourses,
            selectedCourseFilter = updatedSelectedCourseFilter,
            tableServiceChargeApplied = updatedTableServiceChargeApplied,
            tableServiceChargeManuallyRemoved = updatedTableServiceChargeManuallyRemoved,
            showCart = newTableOrder.carts?.isNotEmpty() ?: false
        )
    }
}

/**
 * Set the selected table index
 */
fun setSelectedTableIndex(index: Int) {
    setState {
        if (index < selectedTables.size) {
            val newTableId = selectedTables[index].tableId
            val newTableOrder = tableOrders[newTableId] ?: Order()
            copy(
                selectedTableIndex = index,
                showCart = true // Always show cart when table is selected, even if empty
            )
        } else {
            copy(selectedTableIndex = index)
        }
    }
}

/**
 * Get the currently selected table
 */
//    fun getCurrentSelectedTable(): AreaTableSelectionHelper.AreaTableSelection? {
//        return withState(this) { state ->
//            if (state.selectedTables.isNotEmpty() && state.selectedTableIndex < state.selectedTables.size) {
//                state.selectedTables[state.selectedTableIndex]
//            } else null
//        }
//    }

private fun monitorSyncStatus() {
    viewModelScope.launch {
        orderSyncManager.getLastSyncResult().collect { result ->
            setState {
                copy(
                    syncStatus = when {
                        result == null -> SyncStatus.Idle
                        result.isSuccess -> SyncStatus.Success(
                            syncedCount = result.syncedCount,
                            totalCount = result.totalCount
                        )

                        else -> SyncStatus.Error(
                            errorMessage = result.errorMessage ?: "Unknown error",
                            failedCount = result.failedCount,
                            totalCount = result.totalCount
                        )
                    }
                )
            }
        }
    }
}

suspend fun placeOrderOffline(order: Order): StateFlow<Async<OrderResponse2>> {
    val flow = MutableStateFlow<Async<OrderResponse2>>(Loading())
    setState {
        copy(orderResponse = Loading())
    }
    offlineOrderUseCase(order).execute {
        when (it) {
            is Success -> {
                flow.value = it()
                viewModelScope.launch {
                    orderSyncManager.triggerImmediateSync()
                }
                copy(orderResponse = it())
            }

            else -> {
                flow.value = Uninitialized
                copy(orderResponse = Uninitialized)
            }
        }
    }
    return flow
}

suspend fun cloudPrint(request: CloudPrintRequest): StateFlow<Async<OrderResponse2>> {
    val flow = MutableStateFlow<Async<OrderResponse2>>(Loading())
    setState {
        copy(orderResponse = Loading())
    }
    cloudPrintUseCase(request).execute {
        when (it) {
            is Success -> {
                flow.value = it()
                copy(orderResponse = it())
            }

            else -> {
                flow.value = Uninitialized
                copy(orderResponse = Uninitialized)
            }
        }
    }
    return flow
}

fun isMyGuava(): Boolean {
    val storeId = prefs.store?.id
    return prefs.loginResponse?.stores?.firstOrNull { it.id == storeId }?.paymentProcessorName == "My Guava"
}

fun isSumUp(): Boolean {
    return false
}


fun getCourseCartItems(
    currentOrder: Order,
    existingAssignments: List<CartItemWithCourse>
): List<CartItemWithCourse> {
    val updatedCartItems = currentOrder.carts?.map { cart ->
        existingAssignments[cart.storeItem?.id ?: 0]
    } ?: emptyList()

    return updatedCartItems
}

/**
 * Add a new course to the available courses list (table-specific or global)
 * Auto-selects the newly added course for new items
 */
fun addNewCourse(courseName: String, availableCourses: List<MealCourse>) {
    setState {
        val newCourse = MealCourse(
            name = courseName,
        )
        val updatedCourses = availableCourses.toMutableList()
        updatedCourses.add(newCourse)

        val currentTableId = getCurrentTableId()
        if (currentTableId != null) {
            // Update table-specific courses
            val updatedTableCourses = tableAvailableCourses.toMutableMap()
            updatedTableCourses[currentTableId] = updatedCourses

            // Auto-select the newly added course for new items
            val updatedTableSelectedCourses = tableSelectedCourseForNewItems.toMutableMap()
            updatedTableSelectedCourses[currentTableId] = courseName

            copy(
                tableAvailableCourses = updatedTableCourses,
                tableSelectedCourseForNewItems = updatedTableSelectedCourses
            )
        } else {
            // Update global courses and auto-select the new course
            copy(
                availableCourses = updatedCourses,
                selectedCourseForNewItems = courseName
            )
        }
    }
}

/**
 * Add default courses (Starters, Mains, Desserts) to the available courses list
 * Only adds courses that don't already exist (table-specific or global)
 */
fun addDefaultCourses() {
    setState {
        val defaultCourses = listOf(
            MealCourse("Starters"),
            MealCourse("Mains"),
            MealCourse("Desserts")
        )

        val currentTableId = getCurrentTableId()
        if (currentTableId != null) {
            // Handle table-specific courses
            val currentTableCourses = tableAvailableCourses[currentTableId] ?: emptyList()
            val existingCourseIds = currentTableCourses.map { it.name }.toSet()
            val coursesToAdd = defaultCourses.filter { it.name !in existingCourseIds }

            if (coursesToAdd.isNotEmpty()) {
                val wasEmpty = currentTableCourses.isEmpty()
                val updatedCourses = currentTableCourses.toMutableList()
                updatedCourses.addAll(coursesToAdd)

                // Update table-specific courses
                val updatedTableCourses = tableAvailableCourses.toMutableMap()
                updatedTableCourses[currentTableId] = updatedCourses

                // If we're adding courses to an empty list, set the first course as active
                val updatedTableActiveCourses = if (wasEmpty) {
                    tableActiveCourses.toMutableMap().apply {
                        this[currentTableId] = updatedCourses.firstOrNull()?.name ?: ""
                    }
                } else {
                    tableActiveCourses
                }

                // Auto-select the first newly added course for new items
                val updatedTableSelectedCourses =
                    tableSelectedCourseForNewItems.toMutableMap().apply {
                        this[currentTableId] =
                            coursesToAdd.firstOrNull()?.name ?: updatedCourses.firstOrNull()?.name
                                    ?: ""
                    }

                copy(
                    tableAvailableCourses = updatedTableCourses,
                    tableActiveCourses = updatedTableActiveCourses,
                    tableSelectedCourseForNewItems = updatedTableSelectedCourses
                )
            } else {
                // No changes needed if all default courses already exist
                this
            }
        } else {
            // Handle global courses
            val existingCourseIds = availableCourses.map { it.name }.toSet()
            val coursesToAdd = defaultCourses.filter { it.name !in existingCourseIds }

            if (coursesToAdd.isNotEmpty()) {
                val wasEmpty = availableCourses.isEmpty()
                val updatedCourses = availableCourses.toMutableList()
                updatedCourses.addAll(coursesToAdd)

                val newCurrentActiveCourse = if (wasEmpty) {
                    updatedCourses.firstOrNull()?.name
                } else {
                    currentActiveCourse
                }

                // Auto-select the first newly added course for new items
                val newSelectedCourse =
                    coursesToAdd.firstOrNull()?.name ?: updatedCourses.firstOrNull()?.name ?: ""

                copy(
                    availableCourses = updatedCourses,
                    currentActiveCourse = newCurrentActiveCourse,
                    selectedCourseForNewItems = newSelectedCourse
                )
            } else {
                // No changes needed if all default courses already exist
                this
            }
        }
    }
}

/**
 * Add a new numbered course automatically (Course 1, Course 2, etc.) - table-specific or global
 */
fun addNumberedCourse() {
    setState {
        val currentTableId = getCurrentTableId()

        if (currentTableId != null) {
            // Handle table-specific courses
            val currentTableCourses = tableAvailableCourses[currentTableId] ?: emptyList()
            val courseNumber = currentTableCourses.size + 1
            val courseName = "Course $courseNumber"
            val newCourseId = "course_$courseNumber"

            val newCourse = MealCourse(
                name = courseName,
            )

            val updatedCourses = currentTableCourses.toMutableList()
            updatedCourses.add(newCourse)

            // Update table-specific courses
            val updatedTableCourses = tableAvailableCourses.toMutableMap()
            updatedTableCourses[currentTableId] = updatedCourses

            // Auto-select the newly added course for new items
            val updatedTableSelectedCourses = tableSelectedCourseForNewItems.toMutableMap().apply {
                this[currentTableId] = courseName // Use course name for consistency
            }

            // If this is the first course, set it as active course
            val updatedTableActiveCourses = if (courseNumber == 1) {
                tableActiveCourses.toMutableMap().apply {
                    this[currentTableId] = newCourseId
                }
            } else {
                tableActiveCourses
            }

            copy(
                tableAvailableCourses = updatedTableCourses,
                tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                tableActiveCourses = updatedTableActiveCourses
            )
        } else {
            // Handle global courses
            val courseNumber = availableCourses.size + 1
            val courseName = "Course $courseNumber"
            val newCourseId = "course_$courseNumber"

            val newCourse = MealCourse(
                name = courseName
            )

            val updatedCourses = availableCourses.toMutableList()
            updatedCourses.add(newCourse)

            // Auto-select the newly added course for new items
            val newSelectedCourse = courseName // Use course name for consistency

            val newCurrentActiveCourse = if (courseNumber == 1) {
                newCourseId
            } else {
                currentActiveCourse
            }

            copy(
                availableCourses = updatedCourses,
                selectedCourseForNewItems = newSelectedCourse,
                currentActiveCourse = newCurrentActiveCourse
            )
        }
    }
}

/**
 * Ensure at least one course exists, creating "Course 1" if needed
 */
private fun ensureAtLeastOneCourse(state: ProductsScreenState): ProductsScreenState {
    return state
}

/**
 * Set the selected course for new items (table-specific or global)
 */
fun setSelectedCourseForNewItems(courseId: String) {
    setState {
        val currentTableId = getCurrentTableId()
        if (currentTableId != null) {
            // Update table-specific selected course
            val updatedTableSelectedCourses = tableSelectedCourseForNewItems.toMutableMap()
            updatedTableSelectedCourses[currentTableId] = courseId
            copy(tableSelectedCourseForNewItems = updatedTableSelectedCourses)
        } else {
            // Update global selected course
            copy(selectedCourseForNewItems = courseId)
        }
    }
}

/**
 * Remove a course from available courses
 * @param courseId The ID of the course to remove
 * @return true if course was removed, false if it's the last course
 */
fun removeCourse(courseId: String, state: ProductsScreenState): Boolean {
    val currentTableId = getCurrentTableId(
        selectedTableIndex = state.selectedTableIndex,
        selectedTables = state.selectedTables
    )
    val currentCourses = if (currentTableId != null) {
        state.tableAvailableCourses[currentTableId] ?: emptyList()
    } else {
        state.availableCourses
    }

    return if (currentCourses.size <= 1) {
        // Cannot remove the last course
        false
    } else {
        setState {
            if (currentTableId != null) {
                // Handle table-specific course removal
                val updatedCourses = currentCourses.toMutableList()
                updatedCourses.removeAll { it.name == courseId }

                // Update table-specific courses
                val updatedTableCourses = tableAvailableCourses.toMutableMap()
                updatedTableCourses[currentTableId] = updatedCourses

                // If the removed course was the selected course for new items, select the first remaining course
                val updatedTableSelectedCourses =
                    if (tableSelectedCourseForNewItems[currentTableId] == courseId) {
                        tableSelectedCourseForNewItems.toMutableMap().apply {
                            this[currentTableId] = updatedCourses.firstOrNull()?.name ?: ""
                        }
                    } else {
                        tableSelectedCourseForNewItems
                    }

                // Clean up table-specific course statuses and active courses for the removed course
                val updatedTableCourseStatuses = tableCourseStatuses.toMutableMap()
                val currentTableStatuses =
                    updatedTableCourseStatuses[currentTableId]?.toMutableMap() ?: mutableMapOf()
                currentTableStatuses.remove(courseId)
                updatedTableCourseStatuses[currentTableId] = currentTableStatuses

                val updatedTableActiveCourses =
                    if (tableActiveCourses[currentTableId] == courseId) {
                        tableActiveCourses.toMutableMap().apply {
                            this[currentTableId] = updatedCourses.firstOrNull()?.name ?: ""
                        }
                    } else {
                        tableActiveCourses
                    }

                copy(
                    tableAvailableCourses = updatedTableCourses,
                    tableSelectedCourseForNewItems = updatedTableSelectedCourses,
                    tableCourseStatuses = updatedTableCourseStatuses,
                    tableActiveCourses = updatedTableActiveCourses
                )
            } else {
                // Handle global course removal
                val updatedCourses = availableCourses.toMutableList()
                updatedCourses.removeAll { it.name == courseId }

                // If the removed course was the selected course for new items, select the first remaining course
                val newSelectedCourse = if (selectedCourseForNewItems == courseId) {
                    updatedCourses.firstOrNull()?.name ?: ""
                } else {
                    selectedCourseForNewItems
                }

                // Clean up course statuses and active courses for the removed course
                val updatedCourseStatuses = courseStatuses.toMutableMap()
                updatedCourseStatuses.remove(courseId)

                val updatedTableCourseStatuses =
                    tableCourseStatuses.mapValues { (_, courseStatuses) ->
                        courseStatuses.toMutableMap().apply { remove(courseId) }
                    }

                val updatedTableActiveCourses =
                    tableActiveCourses.mapValues { (_, activeCourse) ->
                        if (activeCourse == courseId) {
                            updatedCourses.firstOrNull()?.name ?: ""
                        } else {
                            activeCourse
                        }
                    }

                val newCurrentActiveCourse = if (currentActiveCourse == courseId) {
                    updatedCourses.firstOrNull()?.name
                } else {
                    currentActiveCourse
                }

                copy(
                    availableCourses = updatedCourses,
                    selectedCourseForNewItems = newSelectedCourse,
                    courseStatuses = updatedCourseStatuses,
                    tableCourseStatuses = updatedTableCourseStatuses,
                    tableActiveCourses = updatedTableActiveCourses,
                    currentActiveCourse = newCurrentActiveCourse
                )
            }
        }
        true
    }
}

/**
 * Edit an existing course (table-specific or global)
 * @param courseId The ID of the course to edit
 * @param newName The new name for the course
 */
fun editCourse(courseId: String, newName: String) {
    setState {
        val currentTableId = getCurrentTableId()
        if (currentTableId != null) {
            // Update table-specific course
            val currentTableCourses = tableAvailableCourses[currentTableId] ?: emptyList()
            val updatedCourses = currentTableCourses.map { course ->
                if (course.name == courseId) {
                    course.copy(name = newName)
                } else {
                    course
                }
            }
            val updatedTableCourses = tableAvailableCourses.toMutableMap()
            updatedTableCourses[currentTableId] = updatedCourses
            copy(tableAvailableCourses = updatedTableCourses)
        } else {
            // Update global course
            val updatedCourses = availableCourses.map { course ->
                if (course.name == courseId) {
                    course.copy(name = newName)
                } else {
                    course
                }
            }
            copy(availableCourses = updatedCourses)
        }
    }
}

/**
 * Update the status of a specific course
 * @param courseId The ID of the course to update
 * @param status The new status for the course
 */
fun updateCourseStatus(courseId: String, status: CourseStatus) {
    setState {
        val currentTableId = getCurrentTableId()
        if (currentTableId != null) {
            // Update table-specific course status
            val updatedTableStatuses = tableCourseStatuses.toMutableMap()
            val currentTableStatuses =
                updatedTableStatuses[currentTableId]?.toMutableMap() ?: mutableMapOf()
            currentTableStatuses[courseId] = status
            updatedTableStatuses[currentTableId] = currentTableStatuses
            copy(tableCourseStatuses = updatedTableStatuses)
        } else {
            // Update global course status (for walk-in customers)
            val updatedStatuses = courseStatuses.toMutableMap()
            updatedStatuses[courseId] = status
            copy(courseStatuses = updatedStatuses)
        }
    }
}

/**
 * Get the status of a specific course
 * @param courseId The ID of the course
 * @return The current status of the course, defaults to GO if not set
 */
fun getCourseStatus(courseId: String, state: ProductsScreenState): CourseStatus {
    val currentTableId = getCurrentTableId(
        selectedTableIndex = state.selectedTableIndex,
        selectedTables = state.selectedTables
    )
    return if (currentTableId != null) {
        // Get table-specific course status
        state.tableCourseStatuses[currentTableId]?.get(courseId) ?: CourseStatus.GO
    } else {
        // Get global course status (for walk-in customers)
        state.courseStatuses[courseId] ?: CourseStatus.GO
    }
}

/**
 * Mark a course as complete
 * @param courseId The ID of the course to mark as complete
 */
fun markCourseAsComplete(courseId: String) {
    updateCourseStatus(courseId, CourseStatus.COMPLETE)
}

/**
 * Reset a course status back to GO
 * @param courseId The ID of the course to reset
 */
fun resetCourseStatus(courseId: String) {
    updateCourseStatus(courseId, CourseStatus.GO)
}

/**
 * Remove course statuses for a specific table
 * @param tableId The ID of the table to remove course statuses for
 */
fun removeTableCourseStatuses(tableId: Int) {
    setState {
        val updatedTableStatuses = tableCourseStatuses.toMutableMap()
        updatedTableStatuses.remove(tableId)

        val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
        updatedTableActiveCourses.remove(tableId)

        copy(
            tableCourseStatuses = updatedTableStatuses,
            tableActiveCourses = updatedTableActiveCourses
        )
    }
}

/**
 * Clear all course statuses for all tables
 */
fun clearAllTableCourseStatuses() {
    setState {
        copy(
            tableCourseStatuses = emptyMap(),
            tableActiveCourses = emptyMap()
        )
    }
}

/**
 * Get course statuses for the current table or global statuses
 * @return Map of course statuses for the current context
 */
fun getCurrentCourseStatuses(state: ProductsScreenState): Map<String, CourseStatus> {
    val currentTableId = getCurrentTableId(
        selectedTableIndex = state.selectedTableIndex,
        selectedTables = state.selectedTables
    )
    return if (currentTableId != null) {
        state.tableCourseStatuses[currentTableId] ?: emptyMap()
    } else {
        state.courseStatuses
    }
}

/**
 * Handle table selection change - initialize course statuses for new table if needed
 * @param tableId The ID of the newly selected table
 */
fun onTableSelected(tableId: Int) {
    setState {
        val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
        // Always set first course as active for new table
        updatedTableActiveCourses[tableId] = availableCourses.firstOrNull()?.name ?: ""
        copy(tableActiveCourses = updatedTableActiveCourses)
    }
}

/**
 * Handle table removal - clean up course statuses for the removed table
 * @param tableId The ID of the table being removed
 */
fun onTableRemoved(tableId: Int) {
    removeTableCourseStatuses(tableId)
}

/**
 * Reset course statuses for the current table
 */
fun resetCurrentTableCourseStatuses(state: ProductsScreenState) {
    val currentTableId = getCurrentTableId(
        selectedTables = state.selectedTables,
        selectedTableIndex = state.selectedTableIndex
    )
    if (currentTableId != null) {
        removeTableCourseStatuses(currentTableId)
        // Re-initialize the table
        onTableSelected(currentTableId)
    } else {
        // Reset global statuses
        setState {
            copy(
                courseStatuses = emptyMap(),
                currentActiveCourse = availableCourses.firstOrNull()?.name
            )
        }
    }
}

/**
 * Check if there are items in the global cart (when no table is selected)
 * @return true if there are items in the global cart
 */
fun hasGlobalCartItems(state: ProductsScreenState): Boolean {
    val currentTableId = getCurrentTableId(
        selectedTableIndex = state.selectedTableIndex,
        selectedTables = state.selectedTables
    )
    return if (currentTableId == null) {
        // No table selected - check global cart
        val globalCartItems = state.order.carts ?: emptyList()
        val globalCartItemsWithCourses = state.globalCartItemsWithCourses
        globalCartItems.isNotEmpty() || globalCartItemsWithCourses.isNotEmpty()
    } else {
        // Table is selected - no global cart items concern
        false
    }
}

/**
 * Clear global cart and course assignments (used when switching from walk-in to table mode)
 */
fun clearGlobalCart() {
    setState {
        copy(
            order = Order(
                carts = emptyList(),
                netPayable = 0.0,
                tax = 0.0,
                totalPrice = 0.0
            ),
            globalCartItemsWithCourses = emptyList(),
            courseStatuses = emptyMap(),
            currentActiveCourse = availableCourses.firstOrNull()?.name
        )
    }
}

/**
 * Assign all global cart items to a selected table
 * This transfers items from global cart to table-specific cart
 */
fun assignGlobalCartToTable(
    selection: AreaTableSelectionHelper.AreaTableSelection,
    state: ProductsScreenState
): ProductsScreenState {

    val globalOrder = state.order
    val globalCartItems = state.globalCartItemsWithCourses

    // Add the table if it doesn't exist
    val updatedTables = state.selectedTables.toMutableList()
    if (!updatedTables.any { it.tableId == selection.tableId }) {
        updatedTables.add(selection)
    }

    // Initialize or update table order with global cart items
    val updatedTableOrders = state.tableOrders.toMutableMap()
    updatedTableOrders[selection.tableId] = globalOrder

    // Initialize table course assignments with global cart items
    val updatedCartItemsWithCourses = state.cartItemsWithCourses.toMutableMap()
    updatedCartItemsWithCourses[selection.tableId] = globalCartItems

    // Initialize other table-specific data
    val updatedFilters = state.selectedCourseFilter.toMutableMap()
    val updatedTableCourses = state.tableAvailableCourses.toMutableMap()
    val updatedTableSelectedCourses = state.tableSelectedCourseForNewItems.toMutableMap()
    val updatedTableActiveCourses = state.tableActiveCourses.toMutableMap()

    if (!updatedFilters.containsKey(selection.tableId)) {
        updatedFilters[selection.tableId] = CourseFilter.ALL
    }
    if (!updatedTableCourses.containsKey(selection.tableId)) {
        updatedTableCourses[selection.tableId] = state.availableCourses
    }
    if (!updatedTableSelectedCourses.containsKey(selection.tableId)) {
        updatedTableSelectedCourses[selection.tableId] =
            state.availableCourses.firstOrNull()?.name ?: ""
    }
    if (!updatedTableActiveCourses.containsKey(selection.tableId)) {
        updatedTableActiveCourses[selection.tableId] =
            state.availableCourses.firstOrNull()?.name ?: ""
    }

    setState {
        // Clear global cart and assign to table
        copy(
            selectedTables = updatedTables,
            selectedTableIndex = updatedTables.indexOfFirst { it.tableId == selection.tableId },
            tableOrders = updatedTableOrders,
            cartItemsWithCourses = updatedCartItemsWithCourses,
            selectedCourseFilter = updatedFilters,
            tableAvailableCourses = updatedTableCourses,
            tableSelectedCourseForNewItems = updatedTableSelectedCourses,
            tableActiveCourses = updatedTableActiveCourses,
            // Clear global cart
            order = Order(
                carts = emptyList(),
                netPayable = 0.0,
                tax = 0.0,
                totalPrice = 0.0
            ),
            globalCartItemsWithCourses = emptyList(),
            courseStatuses = emptyMap(),
            currentActiveCourse = availableCourses.firstOrNull()?.name,
            showCart = true // Show cart after assignment
        )
    }

    return state.copy(
        selectedTables = updatedTables,
        selectedTableIndex = updatedTables.indexOfFirst { it.tableId == selection.tableId },
        tableOrders = updatedTableOrders,
        cartItemsWithCourses = updatedCartItemsWithCourses,
        selectedCourseFilter = updatedFilters,
        tableAvailableCourses = updatedTableCourses,
        tableSelectedCourseForNewItems = updatedTableSelectedCourses,
        tableActiveCourses = updatedTableActiveCourses,
        // Clear global cart
        order = Order(
            carts = emptyList(),
            netPayable = 0.0,
            tax = 0.0,
            totalPrice = 0.0
        ),
        globalCartItemsWithCourses = emptyList(),
        courseStatuses = emptyMap(),
        currentActiveCourse = state.availableCourses.firstOrNull()?.name,
        showCart = true // Show cart after assignment
    )
}

/**
 * Initialize the first course as active (with Go button)
 */
fun initializeActiveCourse() {
    setState {
        val firstCourse = availableCourses.firstOrNull()?.name
        val currentTableId = getCurrentTableId()

        if (currentTableId != null) {
            // Initialize table-specific active course to first course
            val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
            updatedTableActiveCourses[currentTableId] = firstCourse ?: ""
            copy(tableActiveCourses = updatedTableActiveCourses)
        } else {
            // Initialize global active course to first course (for walk-in customers)
            copy(currentActiveCourse = firstCourse)
        }
    }
}

/**
 * Move the Go button to the next course in sequence
 * @param currentCourseId The course that was just processed
 */
fun moveToNextCourse(currentCourseId: String) {
    setState {
        val currentTableId = getCurrentTableId()
        val currentIndex = availableCourses.indexOfFirst { it.name == currentCourseId }
        val nextCourse = if (currentIndex >= 0 && currentIndex < availableCourses.size - 1) {
            availableCourses[currentIndex + 1].name
        } else {
            // If we're at the last course, cycle back to the first course
            availableCourses.firstOrNull()?.name
        }

        if (currentTableId != null) {
            // Update table-specific active course
            val updatedTableActiveCourses = tableActiveCourses.toMutableMap()
            updatedTableActiveCourses[currentTableId] = nextCourse ?: ""
            copy(tableActiveCourses = updatedTableActiveCourses)
        } else {
            // Update global active course (for walk-in customers)
            copy(currentActiveCourse = nextCourse)
        }
    }
}

/**
 * Check if a course should show the Go button
 * @param courseId The course to check
 * @return true if this course should show the Go button
 */
fun shouldShowGoButton(
    courseId: String,
    state: ProductsScreenState
): Boolean {
    val currentTableId = getCurrentTableId(
        selectedTables = state.selectedTables,
        selectedTableIndex = state.selectedTableIndex
    )
    val activeCourse = if (currentTableId != null) {
        // Get table-specific active course
        state.tableActiveCourses[currentTableId] ?: state.availableCourses.firstOrNull()?.name
    } else {
        // Get global active course (for walk-in customers)
        state.currentActiveCourse ?: state.availableCourses.firstOrNull()?.name
    }
    return courseId == activeCourse && getCourseStatus(
        courseId = courseId,
        state = state
    ) == CourseStatus.GO
}

/**
 * Send courses notification to printer for a specific course
 * @param courseId The ID of the course to send notification for
 */
suspend fun sendCoursesNotificationForCourse(
    courseId: String, selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
    selectedTableIndex: Int,
    cartItemsWithCourses: Map<Int, List<CartItemWithCourse>>,
    globalCartItemsWithCourses: List<CartItemWithCourse>,
    availableCourses: List<MealCourse>
): StateFlow<Async<CoursesNotificationResponse>> {
    val flow: MutableStateFlow<Async<CoursesNotificationResponse>> = MutableStateFlow(Loading())

    try {
        val storeId = prefs.store?.id ?: -1
        val currentTableId = getCurrentTableId(
            selectedTables = selectedTables,
            selectedTableIndex = selectedTableIndex
        )
        val tableName = if (currentTableId != null) {
            if (selectedTableIndex >= 0 && selectedTableIndex < selectedTables.size) {
                selectedTables[selectedTableIndex].tableName
            } else {
                "Table-$currentTableId"
            }
        } else {
            "Walk-in"
        }

        // Get cart items based on whether a table is selected or not
        val courseCartItems = if (currentTableId != null) {
            // Table is selected - use table-specific cart items
            cartItemsWithCourses.filter {
                it.value.any { item -> item.courseId == courseId }
            }.values.flatten()
        } else {
            // No table selected - use global cart items
            globalCartItemsWithCourses.filter { it.courseId == courseId }
        }

        val course = availableCourses.find { it.name == courseId }
        val courseName = course?.name ?: ""

        // Extract just the Cart objects from CartItemWithCourse
        val carts = courseCartItems.map { it.cart }

        if (carts.isNotEmpty()) {

            sendCoursesNotificationUseCase(
                storeId = storeId,
                courseName = courseName,
                tableName = tableName ?: "",
                carts = carts
            ).execute { result ->
                when (result) {
                    is Success -> {
                        if (result()()?.success == true) {
                            // Update status to PREPARING when Go button is clicked
                            updateCourseStatus(courseId, CourseStatus.PREPARING)

                            // Handle success - move Go button to next course
                            println("Course notification sent successfully for $courseName")
                            moveToNextCourse(courseId)
                            flow.value = result()
                            // You can add logic here to automatically change to COMPLETE after some time
                            // or wait for kitchen confirmation
                        } else {
                            updateCourseStatus(courseId, CourseStatus.GO)
                            flow.value = Fail(Throwable("Unable to Print Course"))
                        }
                        copy()
                    }

                    is Fail -> {
                        // Handle error - revert status back to GO on failure
                        updateCourseStatus(courseId, CourseStatus.GO)
                        flow.value = Fail(result.error)
                        println("Failed to send course notification for $courseName: ${result.error.message}")
                        copy()
                    }

                    else -> {
                        flow.value = Uninitialized
                        // Loading state
                        println("Sending course notification for $courseName...")
                        copy()
                    }
                }
            }
        } else {
            println("No items found for course: $courseName")
        }
    } catch (e: Exception) {
        println("Error sending course notification: ${e.message}")
    }
    return flow
}

suspend fun printBill(
    selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
    selectedTableIndex: Int,
    cartItemsWithCourses: Map<Int, List<CartItemWithCourse>>,
    globalCartItemsWithCourses: List<CartItemWithCourse>,
    availableCourses: List<MealCourse>
): StateFlow<Async<CoursesNotificationResponse>> {
    val flow: MutableStateFlow<Async<CoursesNotificationResponse>> = MutableStateFlow(Loading())
    try {
        val storeId = prefs.store?.id ?: -1
        val currentTableId = getCurrentTableId(
            selectedTables = selectedTables,
            selectedTableIndex = selectedTableIndex
        )

        // Get cart items based on whether a table is selected or not
        val courseCartItems = if (currentTableId != null) {
            // Table is selected - use table-specific cart items
            cartItemsWithCourses.filter {
                it.value.any { true }
            }.values.flatten()
        } else {
            // No table selected - use global cart items
            globalCartItemsWithCourses.filter { true }
        }

        // Extract just the Cart objects from CartItemWithCourse
        val carts = courseCartItems.map { it.cart }

        if (carts.isNotEmpty()) {
            printRepository.printBill(
                request = PrintBillRequest(
                    storeId = storeId,
                    cartJson = printRepository.convertCartsToJson(
                        carts = carts
                    )
                ),
            ).execute { result ->
                when (result) {
                    is Success -> {
                        if (result()()?.success == true) {
                            // Handle success - move Go button to next course
                            println("Print Bill done successfully")
                            flow.value = result()
                            // You can add logic here to automatically change to COMPLETE after some time
                            // or wait for kitchen confirmation
                        } else {
                            flow.value = Fail(Throwable("Unable to Print Bill"))
                        }
                        copy()
                    }

                    is Fail -> {
                        flow.value = Fail(result.error)
                        println("Unable to Print Bill ${result.error.message}")
                        copy()
                    }

                    else -> {
                        flow.value = Uninitialized
                        copy()
                    }
                }
            }
        } else {
            println("No items found")
        }
    } catch (e: Exception) {
        println("Error sending course notification: ${e.message}")
    }
    return flow
}


suspend fun sendToKitchen(
    selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
    selectedTableIndex: Int,
    cartItemsWithCourses: Map<Int, List<CartItemWithCourse>>,
    globalCartItemsWithCourses: List<CartItemWithCourse>,
    state: ProductsScreenState
): StateFlow<Async<CoursesNotificationResponse>> {
    val flow: MutableStateFlow<Async<CoursesNotificationResponse>> = MutableStateFlow(Loading())
    try {
        val storeId = prefs.store?.id ?: -1
        val currentTableId = getCurrentTableId(
            selectedTables = selectedTables,
            selectedTableIndex = selectedTableIndex
        )

        // Get cart items based on whether a table is selected or not
        val courseCartItems = if (currentTableId != null) {
            // Table is selected - use table-specific cart items
            cartItemsWithCourses.filter {
                it.value.any { true }
            }.values.flatten()
        } else {
            // No table selected - use global cart items
            globalCartItemsWithCourses.filter { true }
        }


        val availableCourses = if (currentTableId != null) {
            // Table is selected - use table-specific cart items
            state.tableAvailableCourses[currentTableId]
        } else {
            // No table selected - use global cart items
            state.availableCourses
        }

        // Extract just the Cart objects from CartItemWithCourse
        // Group cart items by course
        val courseGroups = courseCartItems.groupBy { it.courseId }
        val list = mutableListOf<SendToKitchenItem>()

        courseGroups.forEach { (courseId, items) ->
            val updatedItems = mutableListOf<Cart>()
            items.forEach {
                updatedItems.add(it.cart)
            }

            val item = SendToKitchenItem(
                storeId = storeId,
                courseName = courseId,
                cartJson = printRepository.convertCartsToJson(carts = updatedItems),
                tableName = if (currentTableId != null) {
                    if (selectedTableIndex >= 0 && selectedTableIndex < selectedTables.size) {
                        selectedTables[selectedTableIndex].tableName
                    } else {
                        "Table-$currentTableId"
                    }
                } else {
                    "Walk-in"
                }
            )
            list.add(item)
        }

        if (list.isNotEmpty()) {
            printRepository.sendToKitchen(
                request = SendToKitchenRequest(
                    listItems = list
                ),
            ).execute { result ->
                when (result) {
                    is Success -> {
                        // Handle success - move Go button to next course
                        if (result()()?.success == true) {
                            // Handle success - move Go button to next course
                            println("Print Bill done successfully")
                            state.currentActiveCourse?.let { it ->
                                updateCourseStatus(it, CourseStatus.PREPARING)
                                moveToNextCourse(currentCourseId = it)
                            }
                            flow.value = result()
                            // You can add logic here to automatically change to COMPLETE after some time
                            // or wait for kitchen confirmation
                        } else {
                            flow.value = Fail(Throwable("Unable to Send to Kitchen"))
                        }
                        copy()
                    }

                    is Fail -> {
                        println("Print Bill done successfully ${result.error.message}")
                        copy()
                    }

                    else -> {
                        flow.value = Uninitialized
                        copy()
                    }
                }
            }
        } else {
            println("No items found")
        }
    } catch (e: Exception) {
        println("Error sending course notification: ${e.message}")
    }
    return flow
}

/**
 * Send courses notification to printer
 * This function will send notification for each course that has items in the current order
 */
fun sendCoursesNotification(
    selectedTableIndex: Int, selectedTables: List<AreaTableSelectionHelper.AreaTableSelection>,
    cartItemsWithCourses: Map<Int, List<CartItemWithCourse>>,
    availableCourses: List<MealCourse>
) {
    viewModelScope.launch {
        try {
            val storeId = prefs.store?.id ?: -1
            val currentTableId = getCurrentTableId(
                selectedTableIndex = selectedTableIndex,
                selectedTables = selectedTables
            )
            val tableName = if (currentTableId != null) {
                if (selectedTableIndex >= 0 && selectedTableIndex < selectedTables.size) {
                    selectedTables[selectedTableIndex].tableName
                } else {
                    "Table-$currentTableId"
                }
            } else {
                "Walk-in"
            }

            // Group cart items by course
            val courseGroups = cartItemsWithCourses

            // Send notification for each course that has items
            courseGroups.forEach { (courseId, cartItems) ->
                val course = availableCourses.find { it.name == courseId.toString() }
                val courseName = course?.name ?: "Starters"

                // Extract just the Cart objects from CartItemWithCourse
                val carts = cartItems.map { it.cart }

                if (carts.isNotEmpty()) {
                    sendCoursesNotificationUseCase(
                        storeId = storeId,
                        courseName = courseName,
                        tableName = tableName,
                        carts = carts
                    ).collect { result ->
                        when (result) {
                            is Success -> {
                                // Handle success - maybe show a toast or update UI
                                println("Course notification sent successfully for $courseName")
                            }

                            is Fail -> {
                                // Handle error - maybe show error message
                                println("Failed to send course notification for $courseName: ${result.error.message}")
                            }

                            else -> {
                                // Loading state
                                println("Sending course notification for $courseName...")
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            println("Error sending courses notification: ${e.message}")
        }
    }
}

/**
 * Apply service charge to the current order
 */
fun applyServiceCharge(state: ProductsScreenState) {
    val currentTableId = getCurrentTableId(
        selectedTableIndex = state.selectedTableIndex,
        selectedTables = state.selectedTables
    )
    if (currentTableId != null) {
        // Apply service charge to table-specific order
        val updatedTableServiceCharge = state.tableServiceChargeApplied.toMutableMap()
        updatedTableServiceCharge[currentTableId] = true

        // Update the order totals to include service charge
        val currentOrder = state.tableOrders[currentTableId] ?: Order()
        val serviceChargeAmount =
            (currentOrder.netPayable ?: 0.0) * (getServiceChargePercentage() / 100.0)
        val updatedOrder = currentOrder.copy(
            totalPrice = (currentOrder.netPayable ?: 0.0) + (currentOrder.tax
                ?: 0.0) + serviceChargeAmount
        )
        val updatedTableOrders = state.tableOrders.toMutableMap()
        updatedTableOrders[currentTableId] = updatedOrder

        setState {
            copy(
                tableServiceChargeApplied = updatedTableServiceCharge,
                tableOrders = updatedTableOrders,
                showCart = true // Ensure cart is visible when service charge is applied
            )
        }
    } else {
        // Apply service charge to global order
        val serviceChargeAmount =
            (state.order.netPayable ?: 0.0) * (getServiceChargePercentage() / 100.0)
        val updatedOrder = state.order.copy(
            totalPrice = (state.order.netPayable ?: 0.0) + (state.order.tax
                ?: 0.0) + serviceChargeAmount
        )
        setState {
            copy(
                serviceChargeApplied = true,
                order = updatedOrder,
                showCart = true // Ensure cart is visible when service charge is applied
            )
        }
    }
}

/**
 * Remove service charge from the current order
 */
fun removeServiceCharge(state: ProductsScreenState) {
    val currentTableId = getCurrentTableId(
        selectedTables = state.selectedTables,
        selectedTableIndex = state.selectedTableIndex
    )
    if (currentTableId != null) {
        // Remove service charge from table-specific order
        val updatedTableServiceCharge = state.tableServiceChargeApplied.toMutableMap()
        updatedTableServiceCharge[currentTableId] = false

        // Track that user manually removed service charge for this table
        val updatedTableServiceChargeManuallyRemoved =
            state.tableServiceChargeManuallyRemoved.toMutableMap()
        updatedTableServiceChargeManuallyRemoved[currentTableId] = true

        // Update the order totals to remove service charge
        val currentOrder = state.tableOrders[currentTableId] ?: Order()
        val updatedOrder = currentOrder.copy(
            totalPrice = (currentOrder.netPayable ?: 0.0) + (currentOrder.tax ?: 0.0)
        )
        val updatedTableOrders = state.tableOrders.toMutableMap()
        updatedTableOrders[currentTableId] = updatedOrder

        setState {
            copy(
                tableServiceChargeApplied = updatedTableServiceCharge,
                tableServiceChargeManuallyRemoved = updatedTableServiceChargeManuallyRemoved,
                tableOrders = updatedTableOrders
            )
        }
    } else {
        // Remove service charge from global order
        val updatedOrder = state.order.copy(
            totalPrice = (state.order.netPayable ?: 0.0) + (state.order.tax ?: 0.0)
        )
        setState {
            copy(
                serviceChargeApplied = false,
                order = updatedOrder
            )
        }
    }
}

/**
 * Get service charge percentage from store configurations
 */
fun getServiceChargePercentage(): Double {
    return prefs.storeConfigurations?.data?.serviceChargePercentage ?: 0.0
}

/**
 * Check if service charge should be auto-applied based on store configuration
 */
fun shouldAutoApplyServiceCharge(): Boolean {
    return prefs.storeConfigurations?.data?.serviceChargeEnabledDefault == true
}

/**
 * Initialize service charge for a new table based on store configuration
 * Only auto-apply if user hasn't manually removed service charge for this table before
 */
fun initializeServiceChargeForTable(tableId: Int) {
    if (shouldAutoApplyServiceCharge()) {
        setState {
            val updatedTableServiceCharge = tableServiceChargeApplied.toMutableMap()
            // Only auto-apply if user hasn't manually removed service charge for this table
            val wasManuallyRemoved = tableServiceChargeManuallyRemoved[tableId] ?: false
            if (!wasManuallyRemoved) {
                updatedTableServiceCharge[tableId] = true
            }
            copy(tableServiceChargeApplied = updatedTableServiceCharge)
        }
    }
}

/**
 * Reset the manual removal tracking for a specific table
 * This allows service charge to be auto-applied again for this table
 */
fun resetServiceChargeManualRemovalForTable(tableId: Int) {
    setState {
        val updatedTableServiceChargeManuallyRemoved =
            tableServiceChargeManuallyRemoved.toMutableMap()
        updatedTableServiceChargeManuallyRemoved.remove(tableId)
        copy(tableServiceChargeManuallyRemoved = updatedTableServiceChargeManuallyRemoved)
    }
}

@AssistedFactory
interface Factory : AssistedViewModelFactory<ProductsScreenViewModel, ProductsScreenState> {
    override fun create(state: ProductsScreenState): ProductsScreenViewModel
}

companion object :
    MavericksViewModelFactory<ProductsScreenViewModel, ProductsScreenState> by hiltMavericksViewModelFactory()
}

data class ProductsScreenState(
    val stockItemsResponse: Async<StockItemsResponse> = Uninitialized,
    val stockResponse: Async<StockItemsResponse> = Uninitialized,
    val stock: Int = 0,
    val showUpdateStockDialog: StockItem? = null,
    val isBottomSheetVisible: StockItem? = null,
    val order: Order = Order(), // Deprecated - kept for backward compatibility
    val tableOrders: Map<Int, Order> = emptyMap(), // Table-specific orders (tableId -> Order)
    val orderResponse: Async<OrderResponse2> = Uninitialized,
    val salesResponse: Async<SalesResponse> = Uninitialized,
    val optionDetailsResponse: Async<OptionDetails> = Uninitialized,
    val productTotal: Double = 0.0,
    val isShowPrintingPreview: OrderItem2? = null,
    val shouldPrintInstant: Boolean = false,
    val showCart: Boolean = false,
    val salesReportResponse: Async<SalesReportResponse> = Uninitialized,
    val showSalesReportDialog: Boolean = false,
    val salesRequest: SalesRequest? = null,
    val refreshing: Boolean = false,
    val syncStatus: SyncStatus = SyncStatus.Idle,
    val selectedTables: List<AreaTableSelectionHelper.AreaTableSelection> = emptyList(),
    val selectedTableIndex: Int = 0,
    // Customer management state
    val selectedCustomer: com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer? = null, // Global selected customer (for walk-in customers)
    val tableCustomers: Map<Int, com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer> = emptyMap(), // Table-specific customers (tableId -> RewardsCustomer)
    // Course management state
    val cartItemsWithCourses: Map<Int, List<CartItemWithCourse>> = emptyMap(), // Table-specific course assignments (tableId -> List<CartItemWithCourse>)
    val selectedCourseFilter: Map<Int, CourseFilter> = emptyMap(), // Table-specific course filter (tableId -> CourseFilter)
    val globalCartItemsWithCourses: List<CartItemWithCourse> = emptyList(), // Global course assignments when no tables
    val globalSelectedCourseFilter: CourseFilter = CourseFilter.ALL, // Global course filter when no tables
    val availableCourses: List<MealCourse> = listOf(), // Global courses (for walk-in customers)
    val tableAvailableCourses: Map<Int, List<MealCourse>> = emptyMap(), // Table-specific available courses (tableId -> List<MealCourse>)
    val selectedCourseForNewItems: String = "", // Global selected course for new items (for walk-in customers)
    val tableSelectedCourseForNewItems: Map<Int, String> = emptyMap(), // Table-specific selected course for new items (tableId -> courseId)
    val courseStatuses: Map<String, CourseStatus> = emptyMap(), // Global course statuses (for walk-in customers)
    val tableCourseStatuses: Map<Int, Map<String, CourseStatus>> = emptyMap(), // Table-specific course statuses (tableId -> courseId -> CourseStatus)
    val currentActiveCourse: String? = null, // Global active course (for walk-in customers)
    val tableActiveCourses: Map<Int, String> = emptyMap(), // Table-specific active courses (tableId -> courseId)
    // Service charge state
    val serviceChargeApplied: Boolean = false, // Global service charge applied (for walk-in customers)
    val tableServiceChargeApplied: Map<Int, Boolean> = emptyMap(), // Table-specific service charge applied (tableId -> Boolean)
    val tableServiceChargeManuallyRemoved: Map<Int, Boolean> = emptyMap(), // Track if user manually removed service charge for specific tables (tableId -> Boolean)
) : MavericksState {

    /**
     * Get the current table's order based on selectedTableIndex
     */
    fun getCurrentTableOrder(): Order {
        return if (selectedTables.isNotEmpty() && selectedTableIndex < selectedTables.size && selectedTableIndex >= 0) {
            val currentTable = selectedTables[selectedTableIndex]
            tableOrders[currentTable.tableId] ?: Order()
        } else {
            // Fallback to global order if no tables selected
            order
        }
    }

    /**
     * Get the current table ID, or null if no table is selected
     */
    fun getCurrentTableId(): Int? {
        return if (selectedTables.isNotEmpty() && selectedTableIndex < selectedTables.size && selectedTableIndex >= 0) {
            selectedTables[selectedTableIndex].tableId
        } else {
            null
        }
    }

    /**
     * Get the customer for the current table or global customer
     */
    fun getCurrentCustomer(): com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer? {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            tableCustomers[currentTableId]
        } else {
            selectedCustomer
        }
    }

    /**
     * Get cart items with courses for the current table
     */
    fun getCurrentTableCartItemsWithCourses(): List<CartItemWithCourse> {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            cartItemsWithCourses[currentTableId] ?: emptyList()
        } else {
            // Use global course assignments when no tables selected
            if (globalCartItemsWithCourses.isNotEmpty()) {
                globalCartItemsWithCourses
            } else {
                // Fallback to global cart items with default course assignment
                getCurrentTableOrder().carts?.map { cart ->
                    CartItemWithCourse(
                        cart = cart,
                        courseId = if (getCurrentTableSelectedCourseForNewItems().isNotEmpty()) getCurrentTableSelectedCourseForNewItems() else getCurrentTableAvailableCourses().firstOrNull()?.name
                            ?: ""
                    )
                } ?: emptyList()
            }
        }
    }

    /**
     * Get the current course filter for the current table
     */
    fun getCurrentTableCourseFilter(): CourseFilter {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            selectedCourseFilter[currentTableId] ?: CourseFilter.ALL
        } else {
            // Use global course filter when no tables selected
            globalSelectedCourseFilter
        }
    }

    /**
     * Get filtered cart items based on current table's course filter
     */
    fun getFilteredCartItems(): List<CartItemWithCourse> {
        val cartItems = getCurrentTableCartItemsWithCourses()
        val filter = getCurrentTableCourseFilter()

        return when (filter) {
            CourseFilter.ALL -> cartItems
            CourseFilter.STARTERS -> cartItems.filter { it.courseId == "course_starters" }
            CourseFilter.MAINS -> cartItems.filter { it.courseId == "course_mains" }
            CourseFilter.DESSERTS -> cartItems.filter { it.courseId == "course_desserts" }
        }
    }

    fun isCourseSelected(courseId: String): Boolean {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            tableSelectedCourseForNewItems[currentTableId] == courseId
        } else {
            selectedCourseForNewItems == courseId
        }
    }

    /**
     * Get course counts for the current table
     */
    fun getCourseCounts(): Map<CourseFilter, Int> {
        val cartItems = getCurrentTableCartItemsWithCourses()
        return mapOf(
            CourseFilter.STARTERS to cartItems.count { it.courseId == "course_starters" },
            CourseFilter.MAINS to cartItems.count { it.courseId == "course_mains" },
            CourseFilter.DESSERTS to cartItems.count { it.courseId == "course_desserts" }
        )
    }

    /**
     * Get available courses for the current table or global courses
     */
    fun getCurrentTableAvailableCourses(): List<MealCourse> {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            tableAvailableCourses[currentTableId] ?: emptyList()
        } else {
            availableCourses
        }
    }

    /**
     * Get selected course for new items for the current table or global
     */
    fun getCurrentTableSelectedCourseForNewItems(): String {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            tableSelectedCourseForNewItems[currentTableId] ?: ""
        } else {
            selectedCourseForNewItems
        }
    }

    /**
     * Get service charge amount for the current order
     */
    fun getServiceChargeAmount(serviceChargePercentage: Double): Double {
        if (isServiceChargeApplied()) {
            val currentOrder = getCurrentTableOrder()
            val netPayable = currentOrder.netPayable ?: 0.0
            return netPayable * (serviceChargePercentage / 100.0)
        } else return 0.0
    }

    /**
     * Check if service charge is applied for the current table or global
     */
    fun isServiceChargeApplied(): Boolean {
        val currentTableId = getCurrentTableId()
        return if (currentTableId != null) {
            tableServiceChargeApplied[currentTableId] ?: false
        } else {
            serviceChargeApplied
        }
    }

}